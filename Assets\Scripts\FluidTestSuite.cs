using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

/// <summary>
/// Comprehensive test suite to diagnose and fix fluid simulation issues
/// </summary>
public class FluidTestSuite : MonoBehaviour
{
    [Header("Auto Setup")]
    public bool setupOnStart = true;
    
    [Header("Test Options")]
    public bool testComputeShader = true;
    public bool testCPUFallback = true;
    public bool showBothSideBySide = true;
    
    void Start()
    {
        if (setupOnStart)
        {
            SetupTestSuite();
        }
    }
    
    [ContextMenu("Setup Test Suite")]
    public void SetupTestSuite()
    {
        Debug.Log("FluidTestSuite: Setting up comprehensive fluid simulation tests...");
        
        // Create canvas
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            GameObject canvasGO = new GameObject("TestCanvas");
            canvas = canvasGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasGO.AddComponent<CanvasScaler>();
            canvasGO.AddComponent<GraphicRaycaster>();
        }
        
        // Create EventSystem
        if (FindObjectOfType<EventSystem>() == null)
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystemGO.AddComponent<EventSystem>();
            eventSystemGO.AddComponent<StandaloneInputModule>();
        }
        
        // Create main panel
        GameObject mainPanelGO = new GameObject("FluidTestPanel");
        mainPanelGO.transform.SetParent(canvas.transform, false);
        
        Image mainPanelImage = mainPanelGO.AddComponent<Image>();
        mainPanelImage.color = new Color(0.1f, 0.1f, 0.1f, 1f);
        
        RectTransform mainPanelRect = mainPanelGO.GetComponent<RectTransform>();
        mainPanelRect.anchorMin = Vector2.zero;
        mainPanelRect.anchorMax = Vector2.one;
        mainPanelRect.offsetMin = Vector2.zero;
        mainPanelRect.offsetMax = Vector2.zero;
        
        // Create title
        CreateTitle(mainPanelGO, "FLUID SIMULATION TEST SUITE");
        
        if (showBothSideBySide)
        {
            SetupSideBySideTests(mainPanelGO);
        }
        else
        {
            SetupSingleTest(mainPanelGO);
        }
        
        Debug.Log("FluidTestSuite: Setup complete!");
    }
    
    void CreateTitle(GameObject parent, string titleText)
    {
        GameObject titleGO = new GameObject("Title");
        titleGO.transform.SetParent(parent.transform, false);
        
        Text title = titleGO.AddComponent<Text>();
        title.text = titleText;
        title.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        title.fontSize = 24;
        title.color = Color.white;
        title.alignment = TextAnchor.MiddleCenter;
        
        RectTransform titleRect = titleGO.GetComponent<RectTransform>();
        titleRect.anchorMin = new Vector2(0f, 1f);
        titleRect.anchorMax = new Vector2(1f, 1f);
        titleRect.anchoredPosition = new Vector2(0, -25);
        titleRect.sizeDelta = new Vector2(0, 50);
    }
    
    void SetupSideBySideTests(GameObject parent)
    {
        // Left side - Compute Shader Test
        if (testComputeShader)
        {
            GameObject leftPanelGO = new GameObject("ComputeShaderTest");
            leftPanelGO.transform.SetParent(parent.transform, false);
            
            Image leftPanelImage = leftPanelGO.AddComponent<Image>();
            leftPanelImage.color = new Color(0.2f, 0.1f, 0.1f, 0.5f);
            
            RectTransform leftPanelRect = leftPanelGO.GetComponent<RectTransform>();
            leftPanelRect.anchorMin = new Vector2(0.05f, 0.1f);
            leftPanelRect.anchorMax = new Vector2(0.47f, 0.9f);
            leftPanelRect.offsetMin = Vector2.zero;
            leftPanelRect.offsetMax = Vector2.zero;
            
            // Create Raw Image for compute shader test
            GameObject leftRawImageGO = new GameObject("ComputeRawImage");
            leftRawImageGO.transform.SetParent(leftPanelGO.transform, false);
            
            RawImage leftRawImage = leftRawImageGO.AddComponent<RawImage>();
            leftRawImage.color = Color.white;
            
            RectTransform leftRawImageRect = leftRawImageGO.GetComponent<RectTransform>();
            leftRawImageRect.anchorMin = new Vector2(0.1f, 0.2f);
            leftRawImageRect.anchorMax = new Vector2(0.9f, 0.8f);
            leftRawImageRect.offsetMin = Vector2.zero;
            leftRawImageRect.offsetMax = Vector2.zero;
            
            // Add label
            CreateLabel(leftPanelGO, "COMPUTE SHADER TEST", new Vector2(0.5f, 0.9f));
            
            // Add compute shader test
            ComputeShaderTest computeTest = leftPanelGO.AddComponent<ComputeShaderTest>();
            computeTest.testRawImage = leftRawImage;
            computeTest.showDebugInfo = false; // We'll show our own debug info
            
            // Try to find compute shader
            ComputeShader fluidCompute = FindComputeShader();
            computeTest.fluidCompute = fluidCompute;
        }
        
        // Right side - CPU Fallback Test
        if (testCPUFallback)
        {
            GameObject rightPanelGO = new GameObject("CPUFluidTest");
            rightPanelGO.transform.SetParent(parent.transform, false);
            
            Image rightPanelImage = rightPanelGO.AddComponent<Image>();
            rightPanelImage.color = new Color(0.1f, 0.2f, 0.1f, 0.5f);
            
            RectTransform rightPanelRect = rightPanelGO.GetComponent<RectTransform>();
            rightPanelRect.anchorMin = new Vector2(0.53f, 0.1f);
            rightPanelRect.anchorMax = new Vector2(0.95f, 0.9f);
            rightPanelRect.offsetMin = Vector2.zero;
            rightPanelRect.offsetMax = Vector2.zero;
            
            // Create Raw Image for CPU test
            GameObject rightRawImageGO = new GameObject("CPURawImage");
            rightRawImageGO.transform.SetParent(rightPanelGO.transform, false);
            
            RawImage rightRawImage = rightRawImageGO.AddComponent<RawImage>();
            rightRawImage.color = Color.white;
            
            RectTransform rightRawImageRect = rightRawImageGO.GetComponent<RectTransform>();
            rightRawImageRect.anchorMin = new Vector2(0.1f, 0.2f);
            rightRawImageRect.anchorMax = new Vector2(0.9f, 0.8f);
            rightRawImageRect.offsetMin = Vector2.zero;
            rightRawImageRect.offsetMax = Vector2.zero;
            
            // Add label
            CreateLabel(rightPanelGO, "CPU FALLBACK TEST", new Vector2(0.5f, 0.9f));
            
            // Add CPU fluid simulation
            CPUFluidSim cpuFluid = rightPanelGO.AddComponent<CPUFluidSim>();
            cpuFluid.targetRawImage = rightRawImage;
            cpuFluid.resolution = 128;
            cpuFluid.colorIntensity = 2.0f;
            cpuFluid.autoAddSplats = true;
            cpuFluid.splatInterval = 0.8f;
        }
        
        // Add instructions
        CreateInstructions(parent);
    }
    
    void SetupSingleTest(GameObject parent)
    {
        // Create single test area
        GameObject testPanelGO = new GameObject("SingleTest");
        testPanelGO.transform.SetParent(parent.transform, false);
        
        RectTransform testPanelRect = testPanelGO.GetComponent<RectTransform>();
        testPanelRect.anchorMin = new Vector2(0.2f, 0.2f);
        testPanelRect.anchorMax = new Vector2(0.8f, 0.8f);
        testPanelRect.offsetMin = Vector2.zero;
        testPanelRect.offsetMax = Vector2.zero;
        
        // Create Raw Image
        GameObject rawImageGO = new GameObject("TestRawImage");
        rawImageGO.transform.SetParent(testPanelGO.transform, false);
        
        RawImage rawImage = rawImageGO.AddComponent<RawImage>();
        rawImage.color = Color.white;
        
        RectTransform rawImageRect = rawImageGO.GetComponent<RectTransform>();
        rawImageRect.anchorMin = Vector2.zero;
        rawImageRect.anchorMax = Vector2.one;
        rawImageRect.offsetMin = Vector2.zero;
        rawImageRect.offsetMax = Vector2.zero;
        
        // Try CPU first as it's more reliable
        if (testCPUFallback)
        {
            CPUFluidSim cpuFluid = testPanelGO.AddComponent<CPUFluidSim>();
            cpuFluid.targetRawImage = rawImage;
            cpuFluid.resolution = 128;
            cpuFluid.colorIntensity = 3.0f;
            cpuFluid.autoAddSplats = true;
        }
    }
    
    void CreateLabel(GameObject parent, string labelText, Vector2 anchorPos)
    {
        GameObject labelGO = new GameObject("Label");
        labelGO.transform.SetParent(parent.transform, false);
        
        Text label = labelGO.AddComponent<Text>();
        label.text = labelText;
        label.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        label.fontSize = 16;
        label.color = Color.white;
        label.alignment = TextAnchor.MiddleCenter;
        
        RectTransform labelRect = labelGO.GetComponent<RectTransform>();
        labelRect.anchorMin = anchorPos;
        labelRect.anchorMax = anchorPos;
        labelRect.anchoredPosition = Vector2.zero;
        labelRect.sizeDelta = new Vector2(200, 30);
    }
    
    void CreateInstructions(GameObject parent)
    {
        GameObject instructionsGO = new GameObject("Instructions");
        instructionsGO.transform.SetParent(parent.transform, false);
        
        Text instructions = instructionsGO.AddComponent<Text>();
        instructions.text = "Click and drag on either panel to interact with the fluid simulation.\n" +
                           "Left: GPU Compute Shader | Right: CPU Fallback\n" +
                           "If the left side is black/uniform color, the compute shader isn't working.\n" +
                           "The right side should always show fluid motion.";
        instructions.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        instructions.fontSize = 14;
        instructions.color = new Color(0.8f, 0.8f, 0.8f, 1f);
        instructions.alignment = TextAnchor.MiddleCenter;
        
        RectTransform instructionsRect = instructionsGO.GetComponent<RectTransform>();
        instructionsRect.anchorMin = new Vector2(0f, 0f);
        instructionsRect.anchorMax = new Vector2(1f, 0.1f);
        instructionsRect.offsetMin = Vector2.zero;
        instructionsRect.offsetMax = Vector2.zero;
    }
    
    ComputeShader FindComputeShader()
    {
        // Try Resources first
        ComputeShader compute = Resources.Load<ComputeShader>("Fluid");
        if (compute != null) return compute;
        
        // Try to find in project
#if UNITY_EDITOR
        string[] guids = UnityEditor.AssetDatabase.FindAssets("Fluid t:ComputeShader");
        if (guids.Length > 0)
        {
            string path = UnityEditor.AssetDatabase.GUIDToAssetPath(guids[0]);
            compute = UnityEditor.AssetDatabase.LoadAssetAtPath<ComputeShader>(path);
        }
#endif
        
        return compute;
    }
}