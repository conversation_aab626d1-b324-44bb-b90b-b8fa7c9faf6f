// Simple GPU fluid core for idle animation (no input)
// Kernels: AdvectVelocity, AddSplat (adds dye+velocity), ComputeDivergence, Jacobi, SubtractGradient, AdvectDye

#pragma kernel AdvectVelocity
#pragma kernel AdvectDye
#pragma kernel AddSplat
#pragma kernel ComputeDivergence
#pragma kernel Jacobi
#pragma kernel SubtractGradient
#pragma target 5.0

// external parameters
int2 _SimResolution; // x=width y=height
float _TimeStep;
float _Dissipation; // for velocity or dye
float _VelocityDissipation;
float _PressureDissipation;
float _Radius; // splat radius in normalized coords
float4 _SplatColor; // rgba for dye
float2 _SplatPos; // uv
float2 _SplatForce; // in pixels/sec

// Use RWTexture2D for both read/write to avoid SRV/UAV mismatch
RWTexture2D<float4> _Velocity; // xy = velocity (pixels per sec), zw unused
RWTexture2D<float4> _VelocityOut;

RWTexture2D<float4> _Dye;
RWTexture2D<float4> _DyeOut;

RWTexture2D<float> _Divergence;
RWTexture2D<float> _Pressure;

// Advect velocity (semi-Lagrangian)
[numthreads(8,8,1)]
void AdvectVelocity(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float2 uv = (float2(pix) + 0.5) / float2(_SimResolution);

    // read velocity at this texel (use Load with integer coords)
    int3 coord = int3(pix.x, pix.y, 0);
    float4 velTex = _Velocity[coord.xy];
    float2 vel = velTex.xy;

    // backtrace
    float2 prevUV = uv - (_TimeStep * vel) / float2(_SimResolution);
    prevUV = clamp(prevUV, 0.0, 1.0);

    int2 prevPix = int2(prevUV * float2(_SimResolution));
    prevPix = clamp(prevPix, int2(0,0), _SimResolution - int2(1,1));
    float4 advVelTex = _Velocity[prevPix];
    float2 advVel = advVelTex.xy;

    // apply dissipation
    advVel *= (1.0 - _VelocityDissipation * _TimeStep);

    _VelocityOut[pix] = float4(advVel.xy, 0,0);
}

// Advect dye with bilinear interpolation
[numthreads(8,8,1)]
void AdvectDye(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float2 uv = (float2(pix) + 0.5) / float2(_SimResolution);

    // Read velocity at current position
    float4 velTex = _Velocity[pix];
    float2 vel = velTex.xy;

    // Backtrace to find where this dye came from
    float2 prevUV = uv - (_TimeStep * vel) / float2(_SimResolution);
    prevUV = clamp(prevUV, 0.5 / float2(_SimResolution), 1.0 - 0.5 / float2(_SimResolution));

    // Convert to pixel coordinates for bilinear sampling
    float2 prevPos = prevUV * float2(_SimResolution) - 0.5;
    int2 prevPix0 = int2(floor(prevPos));
    int2 prevPix1 = prevPix0 + int2(1, 1);
    
    // Clamp to valid range
    prevPix0 = clamp(prevPix0, int2(0,0), _SimResolution - int2(1,1));
    prevPix1 = clamp(prevPix1, int2(0,0), _SimResolution - int2(1,1));
    
    // Bilinear interpolation weights
    float2 f = frac(prevPos);
    
    // Sample four neighboring pixels
    float4 c00 = _Dye[int2(prevPix0.x, prevPix0.y)];
    float4 c10 = _Dye[int2(prevPix1.x, prevPix0.y)];
    float4 c01 = _Dye[int2(prevPix0.x, prevPix1.y)];
    float4 c11 = _Dye[int2(prevPix1.x, prevPix1.y)];
    
    // Bilinear interpolation
    float4 c0 = lerp(c00, c10, f.x);
    float4 c1 = lerp(c01, c11, f.x);
    float4 col = lerp(c0, c1, f.y);
    
    // Apply dissipation
    col.rgb *= (1.0 - _Dissipation * _TimeStep);
    col.a = 1.0;

    _DyeOut[pix] = col;
}

// Add a splat: apply gaussian addition to dye and velocity with proper fluid behavior
[numthreads(8,8,1)]
void AddSplat(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float2 uv = (float2(pix) + 0.5) / float2(_SimResolution);
    float2 diff = uv - _SplatPos;
    float dist2 = dot(diff, diff);
    float r = _Radius;
    
    // Proper gaussian falloff - not too wide to maintain fluid behavior
    float w = exp(-dist2 / (r * r));

    // Dye with improved color mixing
    float4 base = _Dye[pix];
    float4 splatColor = float4(_SplatColor.rgb * w, 1.0);
    
    // Use additive blending but with proper intensity control
    float4 mixedDye = base + splatColor * 0.5; // Moderate intensity to allow proper mixing
    
    // Clamp to reasonable range to prevent oversaturation
    mixedDye.rgb = min(mixedDye.rgb, float3(2.0, 2.0, 2.0));
    mixedDye.a = 1.0;
    
    _DyeOut[pix] = mixedDye;

    // Velocity - keep it simple and effective
    float2 baseV = _Velocity[pix].xy;
    float2 addedV = _SplatForce * w;
    _VelocityOut[pix] = float4(baseV + addedV, 0, 0);
}

// Compute divergence of velocity
[numthreads(8,8,1)]
void ComputeDivergence(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    int2 left = max(pix + int2(-1,0), int2(0,0));
    int2 right = min(pix + int2(1,0), _SimResolution - int2(1,1));
    int2 down = max(pix + int2(0,-1), int2(0,0));
    int2 up = min(pix + int2(0,1), _SimResolution - int2(1,1));

    float2 vl = _Velocity[left].xy;
    float2 vr = _Velocity[right].xy;
    float2 vd = _Velocity[down].xy;
    float2 vu = _Velocity[up].xy;

    float hx = 1.0 / _SimResolution.x;
    float hy = 1.0 / _SimResolution.y;

    float dx = (vr.x - vl.x) * 0.5 / hx;
    float dy = (vu.y - vd.y) * 0.5 / hy;

    float div = dx + dy;

    _Divergence[pix] = div;
}

// Jacobi solve for pressure
static const float alpha = -1.0;
static const float inverseBeta = 0.25;

[numthreads(8,8,1)]
void Jacobi(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    int2 left = max(pix + int2(-1,0), int2(0,0));
    int2 right = min(pix + int2(1,0), _SimResolution - int2(1,1));
    int2 down = max(pix + int2(0,-1), int2(0,0));
    int2 up = min(pix + int2(0,1), _SimResolution - int2(1,1));

    float pL = _Pressure[left];
    float pR = _Pressure[right];
    float pD = _Pressure[down];
    float pU = _Pressure[up];
    float b = _Divergence[pix];

    float p = (pL + pR + pD + pU + alpha * b) * inverseBeta;
    // apply slight dissipation
    p *= (1.0 - _PressureDissipation * _TimeStep);
    _Pressure[pix] = p;
}

// Subtract pressure gradient from velocity
[numthreads(8,8,1)]
void SubtractGradient(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    int2 left = max(pix + int2(-1,0), int2(0,0));
    int2 right = min(pix + int2(1,0), _SimResolution - int2(1,1));
    int2 down = max(pix + int2(0,-1), int2(0,0));
    int2 up = min(pix + int2(0,1), _SimResolution - int2(1,1));

    float pL = _Pressure[left];
    float pR = _Pressure[right];
    float pD = _Pressure[down];
    float pU = _Pressure[up];

    float hx = 1.0 / _SimResolution.x;
    float hy = 1.0 / _SimResolution.y;

    float2 vel = _Velocity[pix].xy;
    vel.x -= 0.5 * (pR - pL) / hx;
    vel.y -= 0.5 * (pU - pD) / hy;

    // dissipation
    vel *= (1.0 - _VelocityDissipation * _TimeStep);

    _VelocityOut[pix] = float4(vel,0,0);
}
