using UnityEngine;
using UnityEngine.UI;
using TMPro;
using Cysharp.Threading.Tasks;
using Cysharp.Threading.Tasks.Linq;
using LitMotion;
using LitMotion.Extensions;

namespace UGUIAnimationSamples
{
    public sealed class TextAnimation : MonoBehaviour
    {
        [Header("Components")]
        [SerializeField] Button button1;
        [SerializeField] Button button2;
        [SerializeField] Button button3;
        [SerializeField] Button button4;
        [SerializeField] Button button5;
        [SerializeField] Button button6;
        [SerializeField] TMP_Text text;

        Color color;

        const string ColorCode = "#FF5353";
        static readonly string FillerTextWithRichtext = $"<color={ColorCode}>Grumpy wizards</color> make <b>toxic brew</b> for the evil <i>Queen</i> and <i>Jack</i>.";
        static readonly string FillerText = "Grumpy wizards make toxic brew for the evil Queen and <PERSON>.";

        readonly CompositeMotionHandle motionHandles = new();

        void Start()
        {
            ColorUtility.TryParseHtmlString(ColorCode, out color);

            button1.OnClickAsAsyncEnumerable()
                .Subscribe(_ => Animation1())
                .AddTo(destroyCancellationToken);

            button2.OnClickAsAsyncEnumerable()
                .Subscribe(_ => Animation2())
                .AddTo(destroyCancellationToken);

            button3.OnClickAsAsyncEnumerable()
                .Subscribe(_ => Animation3())
                .AddTo(destroyCancellationToken);

            button4.OnClickAsAsyncEnumerable()
                .Subscribe(_ => Animation4())
                .AddTo(destroyCancellationToken);

            button5.OnClickAsAsyncEnumerable()
                .Subscribe(_ => Animation5())
                .AddTo(destroyCancellationToken);

            button6.OnClickAsAsyncEnumerable()
                .Subscribe(_ => Animation6())
                .AddTo(destroyCancellationToken);
        }

        void OnDestroy()
        {
            motionHandles.Complete();
        }

        void Animation1()
        {
            motionHandles.Complete();

            LMotion.String.Create512Bytes("", FillerTextWithRichtext, 0.05f * FillerText.Length)
                .WithRichText()
                .BindWithState(text, (fs, tmp) =>
                {
                    if (tmp == null) return;
                    tmp.text = fs.ToString();
                })
                .AddTo(motionHandles);
        }

        void Animation2()
        {
            motionHandles.Complete();

            LMotion.String.Create512Bytes("", FillerTextWithRichtext, 0.05f * FillerText.Length)
                .WithRichText()
                .WithScrambleChars(ScrambleMode.Lowercase)
                .BindWithState(text, (fs, tmp) =>
                {
                    if (tmp == null) return;
                    tmp.text = fs.ToString();
                })
                .AddTo(motionHandles);
        }

        void Animation3()
        {
            motionHandles.Complete();

            text.text = FillerText;
            text.ForceMeshUpdate(true);

            for (var i = 0; i < text.textInfo.characterCount; i++)
            {
                var index = i; // capture

                LMotion.Create(-90f, 0f, 0.25f)
                    .WithEase(Ease.OutBack)
                    .WithDelay(i * 0.05f, skipValuesDuringDelay: false)
                    .BindWithState(text, (angle, tmp) =>
                    {
                        if (tmp == null) return;
                        tmp.ForceMeshUpdate(true);
                        if (index >= tmp.textInfo.characterCount) return;

                        var charInfo = tmp.textInfo.characterInfo[index];
                        if (!charInfo.isVisible) return;

                        int materialIndex = charInfo.materialReferenceIndex;
                        int vertexIndex = charInfo.vertexIndex;

                        var meshInfo = tmp.textInfo.meshInfo[materialIndex];

                        // compute center
                        var v0 = meshInfo.vertices[vertexIndex + 0];
                        var v2 = meshInfo.vertices[vertexIndex + 2];
                        var center = (v0 + v2) * 0.5f;

                        var rot = Quaternion.Euler(0f, angle, 0f);

                        // apply rotation around center
                        for (int vi = 0; vi < 4; vi++)
                        {
                            var orig = tmp.textInfo.meshInfo[materialIndex].vertices[vertexIndex + vi];
                            var dir = orig - center;
                            meshInfo.vertices[vertexIndex + vi] = center + rot * dir;
                        }

                        meshInfo.mesh.vertices = meshInfo.vertices;
                        tmp.UpdateVertexData(TMP_VertexDataUpdateFlags.Vertices);
                    })
                    .AddTo(motionHandles);
            }
        }

        void Animation4()
        {
            motionHandles.Complete();

            text.text = FillerText;
            text.ForceMeshUpdate(true);

            for (var i = 0; i < text.textInfo.characterCount; i++)
            {
                var index = i; // capture

                LMotion.Create(Vector3.zero, Vector3.one, 0.2f)
                    .WithEase(Ease.OutSine)
                    .WithDelay(i * 0.05f, skipValuesDuringDelay: false)
                    .BindWithState(text, (scale, tmp) =>
                    {
                        if (tmp == null) return;
                        tmp.ForceMeshUpdate(true);
                        if (index >= tmp.textInfo.characterCount) return;

                        var charInfo = tmp.textInfo.characterInfo[index];
                        if (!charInfo.isVisible) return;

                        int materialIndex = charInfo.materialReferenceIndex;
                        int vertexIndex = charInfo.vertexIndex;

                        var meshInfo = tmp.textInfo.meshInfo[materialIndex];

                        // compute center
                        var v0 = meshInfo.vertices[vertexIndex + 0];
                        var v2 = meshInfo.vertices[vertexIndex + 2];
                        var center = (v0 + v2) * 0.5f;

                        for (int vi = 0; vi < 4; vi++)
                        {
                            var orig = tmp.textInfo.meshInfo[materialIndex].vertices[vertexIndex + vi];
                            var dir = orig - center;
                            meshInfo.vertices[vertexIndex + vi] = center + Vector3.Scale(dir, scale);
                        }

                        meshInfo.mesh.vertices = meshInfo.vertices;
                        tmp.UpdateVertexData(TMP_VertexDataUpdateFlags.Vertices);
                    })
                    .AddTo(motionHandles);

                LMotion.Create(-50f, 0f, 0.2f)
                    .WithEase(Ease.OutSine)
                    .WithDelay(i * 0.05f, skipValuesDuringDelay: false)
                    .BindWithState(text, (y, tmp) =>
                    {
                        if (tmp == null) return;
                        tmp.ForceMeshUpdate(true);
                        if (index >= tmp.textInfo.characterCount) return;

                        var charInfo = tmp.textInfo.characterInfo[index];
                        if (!charInfo.isVisible) return;

                        int materialIndex = charInfo.materialReferenceIndex;
                        int vertexIndex = charInfo.vertexIndex;

                        var meshInfo = tmp.textInfo.meshInfo[materialIndex];

                        for (int vi = 0; vi < 4; vi++)
                        {
                            var orig = tmp.textInfo.meshInfo[materialIndex].vertices[vertexIndex + vi];
                            meshInfo.vertices[vertexIndex + vi] = new Vector3(orig.x, orig.y + y, orig.z);
                        }

                        meshInfo.mesh.vertices = meshInfo.vertices;
                        tmp.UpdateVertexData(TMP_VertexDataUpdateFlags.Vertices);
                    })
                    .AddTo(motionHandles);

                LMotion.Create(Color.white, color, 0.2f)
                    .WithEase(Ease.OutSine)
                    .WithDelay(0.3f + i * 0.05f, skipValuesDuringDelay: false)
                    .BindWithState(text, (c, tmp) =>
                    {
                        if (tmp == null) return;
                        tmp.ForceMeshUpdate(true);
                        if (index >= tmp.textInfo.characterCount) return;

                        var charInfo = tmp.textInfo.characterInfo[index];
                        if (!charInfo.isVisible) return;

                        int materialIndex = charInfo.materialReferenceIndex;
                        int vertexIndex = charInfo.vertexIndex;

                        var meshInfo = tmp.textInfo.meshInfo[materialIndex];

                        var col32 = (Color32)c;
                        for (int vi = 0; vi < 4; vi++)
                        {
                            meshInfo.colors32[vertexIndex + vi] = col32;
                        }

                        meshInfo.mesh.colors32 = meshInfo.colors32;
                        tmp.UpdateVertexData(TMP_VertexDataUpdateFlags.Colors32);
                    })
                    .AddTo(motionHandles);
            }
        }

        void Animation5()
        {
            motionHandles.Complete();

            text.text = FillerText;
            text.ForceMeshUpdate(true);

            for (var i = 0; i < text.textInfo.characterCount; i++)
            {
                var index = i; // capture

                LMotion.Punch.Create(Vector3.one, Vector3.one * 0.7f, 1.4f)
                    .WithEase(Ease.OutQuad)
                    .WithDelay(i * 0.025f, skipValuesDuringDelay: false)
                    .WithFrequency(7)
                    .BindWithState(text, (scale, tmp) =>
                    {
                        if (tmp == null) return;
                        tmp.ForceMeshUpdate(true);
                        if (index >= tmp.textInfo.characterCount) return;

                        var charInfo = tmp.textInfo.characterInfo[index];
                        if (!charInfo.isVisible) return;

                        int materialIndex = charInfo.materialReferenceIndex;
                        int vertexIndex = charInfo.vertexIndex;

                        var meshInfo = tmp.textInfo.meshInfo[materialIndex];

                        // compute center
                        var v0 = meshInfo.vertices[vertexIndex + 0];
                        var v2 = meshInfo.vertices[vertexIndex + 2];
                        var center = (v0 + v2) * 0.5f;

                        for (int vi = 0; vi < 4; vi++)
                        {
                            var orig = tmp.textInfo.meshInfo[materialIndex].vertices[vertexIndex + vi];
                            var dir = orig - center;
                            meshInfo.vertices[vertexIndex + vi] = center + Vector3.Scale(dir, scale);
                        }

                        meshInfo.mesh.vertices = meshInfo.vertices;
                        tmp.UpdateVertexData(TMP_VertexDataUpdateFlags.Vertices);
                    })
                    .AddTo(motionHandles);
            }
        }

        void Animation6()
        {
            motionHandles.Complete();

            text.text = FillerText;
            text.ForceMeshUpdate(true);

            for (var i = 0; i < text.textInfo.characterCount; i++)
            {
                var index = i; // capture
                var shakeDuration = 1.4f * i * 0.025f;

                LMotion.Create(color, Color.white, 0.07f)
                    .WithEase(Ease.OutSine)
                    .WithDelay(i * 0.025f, skipValuesDuringDelay: false)
                    .BindWithState(text, (c, tmp) =>
                    {
                        if (tmp == null) return;
                        tmp.ForceMeshUpdate(true);
                        if (index >= tmp.textInfo.characterCount) return;

                        var charInfo = tmp.textInfo.characterInfo[index];
                        if (!charInfo.isVisible) return;

                        int materialIndex = charInfo.materialReferenceIndex;
                        int vertexIndex = charInfo.vertexIndex;

                        var meshInfo = tmp.textInfo.meshInfo[materialIndex];

                        var col32 = (Color32)c;
                        for (int vi = 0; vi < 4; vi++)
                        {
                            meshInfo.colors32[vertexIndex + vi] = col32;
                        }

                        meshInfo.mesh.colors32 = meshInfo.colors32;
                        tmp.UpdateVertexData(TMP_VertexDataUpdateFlags.Colors32);
                    })
                    .AddTo(motionHandles);

                LMotion.Shake.Create(Vector3.zero, Vector3.one * 30f, shakeDuration)
                    .WithFrequency((int)(shakeDuration / 0.2f))
                    .WithDampingRatio(1)
                    .BindWithState(text, (offset, tmp) =>
                    {
                        if (tmp == null) return;
                        tmp.ForceMeshUpdate(true);
                        if (index >= tmp.textInfo.characterCount) return;

                        var charInfo = tmp.textInfo.characterInfo[index];
                        if (!charInfo.isVisible) return;

                        int materialIndex = charInfo.materialReferenceIndex;
                        int vertexIndex = charInfo.vertexIndex;

                        var meshInfo = tmp.textInfo.meshInfo[materialIndex];

                        for (int vi = 0; vi < 4; vi++)
                        {
                            var orig = tmp.textInfo.meshInfo[materialIndex].vertices[vertexIndex + vi];
                            meshInfo.vertices[vertexIndex + vi] = orig + offset;
                        }

                        meshInfo.mesh.vertices = meshInfo.vertices;
                        tmp.UpdateVertexData(TMP_VertexDataUpdateFlags.Vertices);
                    })
                    .AddTo(motionHandles);
            }
        }
    }
}