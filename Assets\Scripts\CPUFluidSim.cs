using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Simple CPU-based fluid simulation as a fallback
/// This will definitely work and show fluid-like motion
/// </summary>
public class CPUFluidSim : MonoBehaviour
{
    [Header("Settings")]
    public RawImage targetRawImage;
    public int resolution = 128;
    public float viscosity = 0.0001f;
    public float diffusion = 0.0001f;
    public float fadeRate = 0.99f;
    
    [Header("Visual")]
    public float colorIntensity = 2.0f;
    public bool autoAddSplats = true;
    public float splatInterval = 0.5f;
    
    private Texture2D fluidTexture;
    private Color[] pixels;
    private Vector2[] velocity;
    private Color[] dye;
    private int size;
    private float lastSplatTime;
    
    void Start()
    {
        SetupCPUFluid();
    }
    
    void SetupCPUFluid()
    {
        if (targetRawImage == null)
        {
            Debug.LogError("CPUFluidSim: No RawImage assigned!");
            return;
        }
        
        size = resolution;
        
        // Create texture
        fluidTexture = new Texture2D(size, size, TextureFormat.RGBA32, false);
        fluidTexture.filterMode = FilterMode.Bilinear;
        fluidTexture.wrapMode = TextureWrapMode.Clamp;
        
        // Initialize arrays
        pixels = new Color[size * size];
        velocity = new Vector2[size * size];
        dye = new Color[size * size];
        
        // Clear everything
        for (int i = 0; i < pixels.Length; i++)
        {
            pixels[i] = Color.black;
            velocity[i] = Vector2.zero;
            dye[i] = Color.black;
        }
        
        // Assign to Raw Image
        targetRawImage.texture = fluidTexture;
        targetRawImage.material = null;
        targetRawImage.color = Color.white;
        
        Debug.Log("CPUFluidSim: CPU-based fluid simulation initialized");
    }
    
    void Update()
    {
        if (fluidTexture == null) return;
        
        // Auto add splats
        if (autoAddSplats && Time.time - lastSplatTime > splatInterval)
        {
            lastSplatTime = Time.time;
            AddRandomSplat();
        }
        
        // Handle mouse input
        if (Input.GetMouseButton(0))
        {
            HandleMouseInput();
        }
        
        // Update simulation
        UpdateFluidSimulation();
        
        // Update texture
        UpdateTexture();
    }
    
    void AddRandomSplat()
    {
        Vector2 pos = new Vector2(
            Random.Range(0.2f, 0.8f),
            Random.Range(0.2f, 0.8f)
        );
        
        Color color = Color.HSVToRGB(Random.value, 0.8f, 1.0f) * colorIntensity;
        Vector2 force = new Vector2(
            Random.Range(-50f, 50f),
            Random.Range(-50f, 50f)
        );
        
        AddSplat(pos, color, force);
    }
    
    void HandleMouseInput()
    {
        Vector2 mousePos = Input.mousePosition;
        
        // Convert to UV coordinates relative to the Raw Image
        RectTransform rectTransform = targetRawImage.GetComponent<RectTransform>();
        Canvas canvas = targetRawImage.GetComponentInParent<Canvas>();
        Camera uiCamera = canvas.renderMode == RenderMode.ScreenSpaceOverlay ? null : canvas.worldCamera;
        
        if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
            rectTransform, mousePos, uiCamera, out Vector2 localPoint))
        {
            Rect rect = rectTransform.rect;
            Vector2 uv = new Vector2(
                (localPoint.x - rect.x) / rect.width,
                (localPoint.y - rect.y) / rect.height
            );
            
            if (uv.x >= 0 && uv.x <= 1 && uv.y >= 0 && uv.y <= 1)
            {
                Color color = Color.HSVToRGB(Time.time * 0.1f % 1f, 0.8f, 1.0f) * colorIntensity;
                Vector2 force = new Vector2(
                    Input.GetAxis("Mouse X") * 100f,
                    Input.GetAxis("Mouse Y") * 100f
                );
                AddSplat(uv, color, force);
            }
        }
    }
    
    void AddSplat(Vector2 uv, Color color, Vector2 force)
    {
        int x = Mathf.RoundToInt(uv.x * (size - 1));
        int y = Mathf.RoundToInt(uv.y * (size - 1));
        
        float radius = 0.1f * size;
        
        for (int dy = -Mathf.RoundToInt(radius); dy <= Mathf.RoundToInt(radius); dy++)
        {
            for (int dx = -Mathf.RoundToInt(radius); dx <= Mathf.RoundToInt(radius); dx++)
            {
                int px = x + dx;
                int py = y + dy;
                
                if (px >= 0 && px < size && py >= 0 && py < size)
                {
                    float dist = Mathf.Sqrt(dx * dx + dy * dy);
                    if (dist <= radius)
                    {
                        float weight = Mathf.Exp(-dist * dist / (radius * radius * 0.5f));
                        int index = py * size + px;
                        
                        // Add dye
                        dye[index] += color * weight;
                        
                        // Add velocity
                        velocity[index] += force * weight * 0.01f;
                    }
                }
            }
        }
    }
    
    void UpdateFluidSimulation()
    {
        // Multi-step fluid simulation for better quality
        
        // Step 1: Advect velocity
        AdvectVelocity();
        
        // Step 2: Apply viscosity/diffusion to velocity
        DiffuseVelocity();
        
        // Step 3: Apply incompressibility (simplified)
        ProjectVelocity();
        
        // Step 4: Advect dye
        AdvectDye();
        
        // Step 5: Apply dye diffusion for mixing
        DiffuseDye();
    }
    
    void AdvectVelocity()
    {
        Vector2[] newVelocity = new Vector2[velocity.Length];
        
        for (int y = 1; y < size - 1; y++)
        {
            for (int x = 1; x < size - 1; x++)
            {
                int index = y * size + x;
                Vector2 vel = velocity[index];
                
                // Backtrace
                Vector2 pos = new Vector2(x, y) - vel * Time.deltaTime * 20f;
                pos.x = Mathf.Clamp(pos.x, 0.5f, size - 1.5f);
                pos.y = Mathf.Clamp(pos.y, 0.5f, size - 1.5f);
                
                newVelocity[index] = BilinearSampleVelocity(pos);
            }
        }
        
        velocity = newVelocity;
    }
    
    void DiffuseVelocity()
    {
        Vector2[] newVelocity = new Vector2[velocity.Length];
        
        for (int y = 1; y < size - 1; y++)
        {
            for (int x = 1; x < size - 1; x++)
            {
                int index = y * size + x;
                
                // 5-point stencil for diffusion
                Vector2 center = velocity[index];
                Vector2 left = velocity[y * size + (x - 1)];
                Vector2 right = velocity[y * size + (x + 1)];
                Vector2 up = velocity[(y + 1) * size + x];
                Vector2 down = velocity[(y - 1) * size + x];
                
                // Diffusion
                Vector2 diffused = (left + right + up + down + center * 4f) / 8f;
                newVelocity[index] = Vector2.Lerp(center, diffused, viscosity * 100f);
                
                // Apply damping
                newVelocity[index] *= (1f - viscosity);
            }
        }
        
        velocity = newVelocity;
    }
    
    void ProjectVelocity()
    {
        // Simplified incompressibility - make velocity field divergence-free
        Vector2[] newVelocity = new Vector2[velocity.Length];
        
        for (int y = 1; y < size - 1; y++)
        {
            for (int x = 1; x < size - 1; x++)
            {
                int index = y * size + x;
                
                // Calculate divergence
                float divX = (velocity[y * size + (x + 1)].x - velocity[y * size + (x - 1)].x) * 0.5f;
                float divY = (velocity[(y + 1) * size + x].y - velocity[(y - 1) * size + x].y) * 0.5f;
                float div = divX + divY;
                
                // Subtract gradient to reduce divergence
                newVelocity[index] = velocity[index] - new Vector2(divX, divY) * 0.5f;
            }
        }
        
        velocity = newVelocity;
    }
    
    void AdvectDye()
    {
        Color[] newDye = new Color[dye.Length];
        
        for (int y = 1; y < size - 1; y++)
        {
            for (int x = 1; x < size - 1; x++)
            {
                int index = y * size + x;
                Vector2 vel = velocity[index];
                
                // Backtrace
                Vector2 pos = new Vector2(x, y) - vel * Time.deltaTime * 20f;
                pos.x = Mathf.Clamp(pos.x, 0.5f, size - 1.5f);
                pos.y = Mathf.Clamp(pos.y, 0.5f, size - 1.5f);
                
                newDye[index] = BilinearSampleDye(pos);
                
                // Apply fade
                newDye[index] *= fadeRate;
            }
        }
        
        dye = newDye;
    }
    
    void DiffuseDye()
    {
        Color[] newDye = new Color[dye.Length];
        
        for (int y = 1; y < size - 1; y++)
        {
            for (int x = 1; x < size - 1; x++)
            {
                int index = y * size + x;
                
                // 5-point stencil for diffusion (mixing)
                Color center = dye[index];
                Color left = dye[y * size + (x - 1)];
                Color right = dye[y * size + (x + 1)];
                Color up = dye[(y + 1) * size + x];
                Color down = dye[(y - 1) * size + x];
                
                // Mix colors
                Color mixed = (left + right + up + down + center * 4f) / 8f;
                newDye[index] = Color.Lerp(center, mixed, diffusion * 50f);
            }
        }
        
        dye = newDye;
    }
    
    Vector2 BilinearSampleVelocity(Vector2 pos)
    {
        int x0 = Mathf.FloorToInt(pos.x);
        int y0 = Mathf.FloorToInt(pos.y);
        int x1 = Mathf.Min(x0 + 1, size - 1);
        int y1 = Mathf.Min(y0 + 1, size - 1);
        
        float fx = pos.x - x0;
        float fy = pos.y - y0;
        
        Vector2 v00 = velocity[y0 * size + x0];
        Vector2 v10 = velocity[y0 * size + x1];
        Vector2 v01 = velocity[y1 * size + x0];
        Vector2 v11 = velocity[y1 * size + x1];
        
        Vector2 v0 = Vector2.Lerp(v00, v10, fx);
        Vector2 v1 = Vector2.Lerp(v01, v11, fx);
        return Vector2.Lerp(v0, v1, fy);
    }
    
    Color BilinearSampleDye(Vector2 pos)
    {
        int x0 = Mathf.FloorToInt(pos.x);
        int y0 = Mathf.FloorToInt(pos.y);
        int x1 = Mathf.Min(x0 + 1, size - 1);
        int y1 = Mathf.Min(y0 + 1, size - 1);
        
        float fx = pos.x - x0;
        float fy = pos.y - y0;
        
        Color c00 = dye[y0 * size + x0];
        Color c10 = dye[y0 * size + x1];
        Color c01 = dye[y1 * size + x0];
        Color c11 = dye[y1 * size + x1];
        
        Color c0 = Color.Lerp(c00, c10, fx);
        Color c1 = Color.Lerp(c01, c11, fx);
        return Color.Lerp(c0, c1, fy);
    }
    
    void UpdateTexture()
    {
        // Copy dye to pixels
        for (int i = 0; i < pixels.Length; i++)
        {
            pixels[i] = dye[i];
        }
        
        // Update texture
        fluidTexture.SetPixels(pixels);
        fluidTexture.Apply();
    }
    
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 150));
        GUILayout.Label("=== CPU FLUID SIMULATION ===");
        GUILayout.Label($"Resolution: {size}x{size}");
        GUILayout.Label($"Auto Splats: {autoAddSplats}");
        GUILayout.Label("Click and drag to add splats!");
        
        if (GUILayout.Button("Add Random Splat"))
        {
            AddRandomSplat();
        }
        
        GUILayout.EndArea();
    }
    
    void OnDestroy()
    {
        if (fluidTexture != null)
        {
            DestroyImmediate(fluidTexture);
        }
    }
}