using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

/// <summary>
/// Very simple test setup to isolate fluid simulation issues
/// </summary>
public class SimpleTestSetup : MonoBehaviour
{
    [Header("Auto Setup")]
    public bool setupOnStart = true;
    
    void Start()
    {
        if (setupOnStart)
        {
            SetupSimpleTest();
        }
    }
    
    [ContextMenu("Setup Simple Test")]
    public void SetupSimpleTest()
    {
        // Find or create canvas
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            GameObject canvasGO = new GameObject("TestCanvas");
            canvas = canvasGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasGO.AddComponent<CanvasScaler>();
            canvasGO.AddComponent<GraphicRaycaster>();
        }
        
        // Create EventSystem if needed
        if (FindObjectOfType<EventSystem>() == null)
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystemGO.AddComponent<EventSystem>();
            eventSystemGO.AddComponent<StandaloneInputModule>();
        }
        
        // Create a simple panel
        GameObject panelGO = new GameObject("TestPanel");
        panelGO.transform.SetParent(canvas.transform, false);
        
        Image panelImage = panelGO.AddComponent<Image>();
        panelImage.color = new Color(0.2f, 0.2f, 0.2f, 1f);
        
        RectTransform panelRect = panelGO.GetComponent<RectTransform>();
        panelRect.anchorMin = Vector2.zero;
        panelRect.anchorMax = Vector2.one;
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // Create Raw Image for testing
        GameObject rawImageGO = new GameObject("TestRawImage");
        rawImageGO.transform.SetParent(panelGO.transform, false);
        
        RawImage rawImage = rawImageGO.AddComponent<RawImage>();
        rawImage.color = Color.white;
        
        RectTransform rawImageRect = rawImageGO.GetComponent<RectTransform>();
        rawImageRect.anchorMin = new Vector2(0.5f, 0.5f);
        rawImageRect.anchorMax = new Vector2(0.5f, 0.5f);
        rawImageRect.anchoredPosition = Vector2.zero;
        rawImageRect.sizeDelta = new Vector2(400, 400);
        
        // Add title
        GameObject titleGO = new GameObject("TestTitle");
        titleGO.transform.SetParent(panelGO.transform, false);
        
        Text titleText = titleGO.AddComponent<Text>();
        titleText.text = "DIRECT FLUID TEST - Click anywhere to add splats";
        titleText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        titleText.fontSize = 20;
        titleText.color = Color.white;
        titleText.alignment = TextAnchor.MiddleCenter;
        
        RectTransform titleRect = titleGO.GetComponent<RectTransform>();
        titleRect.anchorMin = new Vector2(0f, 1f);
        titleRect.anchorMax = new Vector2(1f, 1f);
        titleRect.anchoredPosition = new Vector2(0, -25);
        titleRect.sizeDelta = new Vector2(0, 50);
        
        // Find the compute shader
        ComputeShader fluidCompute = null;
        
        // Try to find it in Resources first
        fluidCompute = Resources.Load<ComputeShader>("Fluid");
        
        // Try to find it in the project
        if (fluidCompute == null)
        {
#if UNITY_EDITOR
            string[] guids = UnityEditor.AssetDatabase.FindAssets("Fluid t:ComputeShader");
            if (guids.Length > 0)
            {
                string path = UnityEditor.AssetDatabase.GUIDToAssetPath(guids[0]);
                fluidCompute = UnityEditor.AssetDatabase.LoadAssetAtPath<ComputeShader>(path);
            }
#endif
        }
        
        if (fluidCompute == null)
        {
            Debug.LogError("SimpleTestSetup: Could not find Fluid.compute shader!");
            return;
        }
        
        // Add the direct test component
        DirectFluidTest directTest = panelGO.AddComponent<DirectFluidTest>();
        directTest.testRawImage = rawImage;
        directTest.fluidCompute = fluidCompute;
        directTest.showDebugInfo = true;
        directTest.useNoMaterial = true;
        directTest.forceMaxIntensity = true;
        
        Debug.Log("SimpleTestSetup: Direct fluid test setup complete!");
        Debug.Log($"Found compute shader: {fluidCompute != null}");
        Debug.Log("Check the console and on-screen debug info for details.");
        Debug.Log("Click anywhere to add test splats!");
    }
}