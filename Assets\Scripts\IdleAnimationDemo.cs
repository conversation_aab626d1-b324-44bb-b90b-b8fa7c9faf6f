using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Demo script to showcase different idle animation patterns
/// Provides UI controls to switch between patterns and adjust parameters
/// </summary>
public class IdleAnimationDemo : MonoBehaviour
{
    [Header("UI References")]
    public Button spiralButton;
    public Button waveButton;
    public Button orbitButton;
    public Button perlinButton;
    public Button cyclingButton;
    public Slider speedSlider;
    public Slider intensitySlider;
    public Text currentPatternText;
    public Text instructionsText;
    
    [Header("Fluid Reference")]
    public EnhancedFluidSim fluidSim;
    
    private string[] patternNames = { "Spiral", "Wave", "Orbit", "Perlin Noise", "Cycling" };
    
    void Start()
    {
        SetupUI();
        FindFluidSim();
        UpdateUI();
    }
    
    void SetupUI()
    {
        // Create UI if it doesn't exist
        if (spiralButton == null || waveButton == null || orbitButton == null || 
            perlinButton == null || cyclingButton == null)
        {
            CreateDemoUI();
        }
        
        // Setup button listeners
        if (spiralButton != null)
            spiralButton.onClick.AddListener(() => SetPattern(EnhancedFluidSim.IdlePatternType.Spiral));
        if (waveButton != null)
            waveButton.onClick.AddListener(() => SetPattern(EnhancedFluidSim.IdlePatternType.Wave));
        if (orbitButton != null)
            orbitButton.onClick.AddListener(() => SetPattern(EnhancedFluidSim.IdlePatternType.Orbit));
        if (perlinButton != null)
            perlinButton.onClick.AddListener(() => SetPattern(EnhancedFluidSim.IdlePatternType.PerlinNoise));
        if (cyclingButton != null)
            cyclingButton.onClick.AddListener(() => SetPattern(EnhancedFluidSim.IdlePatternType.Cycling));
        
        // Setup slider listeners
        if (speedSlider != null)
        {
            speedSlider.minValue = 0.1f;
            speedSlider.maxValue = 3.0f;
            speedSlider.value = 1.0f;
            speedSlider.onValueChanged.AddListener(SetAnimationSpeed);
        }
        
        if (intensitySlider != null)
        {
            intensitySlider.minValue = 0.5f;
            intensitySlider.maxValue = 3.0f;
            intensitySlider.value = 1.5f;
            intensitySlider.onValueChanged.AddListener(SetIntensity);
        }
    }
    
    void CreateDemoUI()
    {
        // Find or create canvas
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null) return;
        
        // Create control panel
        GameObject controlPanel = new GameObject("IdleAnimationControls");
        controlPanel.transform.SetParent(canvas.transform, false);
        
        Image panelImage = controlPanel.AddComponent<Image>();
        panelImage.color = new Color(0, 0, 0, 0.7f);
        
        RectTransform panelRect = controlPanel.GetComponent<RectTransform>();
        panelRect.anchorMin = new Vector2(0, 1);
        panelRect.anchorMax = new Vector2(0, 1);
        panelRect.anchoredPosition = new Vector2(10, -10);
        panelRect.sizeDelta = new Vector2(250, 300);
        
        // Create title
        GameObject titleGO = new GameObject("Title");
        titleGO.transform.SetParent(controlPanel.transform, false);
        Text titleText = titleGO.AddComponent<Text>();
        titleText.text = "Idle Animation Patterns";
        titleText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        titleText.fontSize = 16;
        titleText.color = Color.white;
        titleText.alignment = TextAnchor.MiddleCenter;
        
        RectTransform titleRect = titleGO.GetComponent<RectTransform>();
        titleRect.anchorMin = new Vector2(0, 1);
        titleRect.anchorMax = new Vector2(1, 1);
        titleRect.anchoredPosition = new Vector2(0, -15);
        titleRect.sizeDelta = new Vector2(0, 30);
        
        // Create buttons
        float buttonY = -50;
        spiralButton = CreateButton(controlPanel, "Spiral", buttonY);
        waveButton = CreateButton(controlPanel, "Wave", buttonY - 35);
        orbitButton = CreateButton(controlPanel, "Orbit", buttonY - 70);
        perlinButton = CreateButton(controlPanel, "Perlin", buttonY - 105);
        cyclingButton = CreateButton(controlPanel, "Cycling", buttonY - 140);
        
        // Create sliders
        speedSlider = CreateSlider(controlPanel, "Speed", buttonY - 180);
        intensitySlider = CreateSlider(controlPanel, "Intensity", buttonY - 220);
        
        // Create current pattern text
        GameObject currentPatternGO = new GameObject("CurrentPattern");
        currentPatternGO.transform.SetParent(controlPanel.transform, false);
        currentPatternText = currentPatternGO.AddComponent<Text>();
        currentPatternText.text = "Current: Cycling";
        currentPatternText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        currentPatternText.fontSize = 12;
        currentPatternText.color = Color.yellow;
        currentPatternText.alignment = TextAnchor.MiddleCenter;
        
        RectTransform currentPatternRect = currentPatternGO.GetComponent<RectTransform>();
        currentPatternRect.anchorMin = new Vector2(0, 0);
        currentPatternRect.anchorMax = new Vector2(1, 0);
        currentPatternRect.anchoredPosition = new Vector2(0, 15);
        currentPatternRect.sizeDelta = new Vector2(0, 20);
        
        // Create instructions
        GameObject instructionsGO = new GameObject("Instructions");
        instructionsGO.transform.SetParent(canvas.transform, false);
        instructionsText = instructionsGO.AddComponent<Text>();
        instructionsText.text = "Click buttons to change idle animation patterns.\n" +
                               "Adjust speed and intensity with sliders.\n" +
                               "Click on fluid to add interactive splats!";
        instructionsText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        instructionsText.fontSize = 12;
        instructionsText.color = new Color(0.8f, 0.8f, 0.8f, 1f);
        instructionsText.alignment = TextAnchor.MiddleLeft;
        
        RectTransform instructionsRect = instructionsGO.GetComponent<RectTransform>();
        instructionsRect.anchorMin = new Vector2(1, 1);
        instructionsRect.anchorMax = new Vector2(1, 1);
        instructionsRect.anchoredPosition = new Vector2(-10, -10);
        instructionsRect.sizeDelta = new Vector2(300, 80);
    }
    
    Button CreateButton(GameObject parent, string text, float yPos)
    {
        GameObject buttonGO = new GameObject($"Button_{text}");
        buttonGO.transform.SetParent(parent.transform, false);
        
        Image buttonImage = buttonGO.AddComponent<Image>();
        buttonImage.color = new Color(0.2f, 0.2f, 0.2f, 1f);
        
        Button button = buttonGO.AddComponent<Button>();
        
        RectTransform buttonRect = buttonGO.GetComponent<RectTransform>();
        buttonRect.anchorMin = new Vector2(0.1f, 1);
        buttonRect.anchorMax = new Vector2(0.9f, 1);
        buttonRect.anchoredPosition = new Vector2(0, yPos);
        buttonRect.sizeDelta = new Vector2(0, 30);
        
        // Button text
        GameObject textGO = new GameObject("Text");
        textGO.transform.SetParent(buttonGO.transform, false);
        Text buttonText = textGO.AddComponent<Text>();
        buttonText.text = text;
        buttonText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        buttonText.fontSize = 14;
        buttonText.color = Color.white;
        buttonText.alignment = TextAnchor.MiddleCenter;
        
        RectTransform textRect = textGO.GetComponent<RectTransform>();
        textRect.anchorMin = Vector2.zero;
        textRect.anchorMax = Vector2.one;
        textRect.offsetMin = Vector2.zero;
        textRect.offsetMax = Vector2.zero;
        
        return button;
    }
    
    Slider CreateSlider(GameObject parent, string label, float yPos)
    {
        GameObject sliderGO = new GameObject($"Slider_{label}");
        sliderGO.transform.SetParent(parent.transform, false);
        
        RectTransform sliderRect = sliderGO.GetComponent<RectTransform>();
        sliderRect.anchorMin = new Vector2(0.1f, 1);
        sliderRect.anchorMax = new Vector2(0.9f, 1);
        sliderRect.anchoredPosition = new Vector2(0, yPos);
        sliderRect.sizeDelta = new Vector2(0, 20);
        
        Slider slider = sliderGO.AddComponent<Slider>();
        
        // Background
        GameObject background = new GameObject("Background");
        background.transform.SetParent(sliderGO.transform, false);
        Image bgImage = background.AddComponent<Image>();
        bgImage.color = new Color(0.1f, 0.1f, 0.1f, 1f);
        RectTransform bgRect = background.GetComponent<RectTransform>();
        bgRect.anchorMin = Vector2.zero;
        bgRect.anchorMax = Vector2.one;
        bgRect.offsetMin = Vector2.zero;
        bgRect.offsetMax = Vector2.zero;
        
        // Fill area
        GameObject fillArea = new GameObject("Fill Area");
        fillArea.transform.SetParent(sliderGO.transform, false);
        RectTransform fillAreaRect = fillArea.GetComponent<RectTransform>();
        fillAreaRect.anchorMin = Vector2.zero;
        fillAreaRect.anchorMax = Vector2.one;
        fillAreaRect.offsetMin = Vector2.zero;
        fillAreaRect.offsetMax = Vector2.zero;
        
        GameObject fill = new GameObject("Fill");
        fill.transform.SetParent(fillArea.transform, false);
        Image fillImage = fill.AddComponent<Image>();
        fillImage.color = new Color(0.3f, 0.6f, 1f, 1f);
        
        // Handle
        GameObject handle = new GameObject("Handle");
        handle.transform.SetParent(sliderGO.transform, false);
        Image handleImage = handle.AddComponent<Image>();
        handleImage.color = Color.white;
        RectTransform handleRect = handle.GetComponent<RectTransform>();
        handleRect.sizeDelta = new Vector2(20, 20);
        
        // Configure slider
        slider.fillRect = fill.GetComponent<RectTransform>();
        slider.handleRect = handleRect;
        slider.targetGraphic = handleImage;
        
        // Label
        GameObject labelGO = new GameObject("Label");
        labelGO.transform.SetParent(sliderGO.transform, false);
        Text labelText = labelGO.AddComponent<Text>();
        labelText.text = label;
        labelText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        labelText.fontSize = 10;
        labelText.color = Color.white;
        labelText.alignment = TextAnchor.MiddleLeft;
        
        RectTransform labelRect = labelGO.GetComponent<RectTransform>();
        labelRect.anchorMin = new Vector2(0, 1);
        labelRect.anchorMax = new Vector2(1, 1);
        labelRect.anchoredPosition = new Vector2(0, 10);
        labelRect.sizeDelta = new Vector2(0, 15);
        
        return slider;
    }
    
    void FindFluidSim()
    {
        if (fluidSim == null)
        {
            fluidSim = FindObjectOfType<EnhancedFluidSim>();
        }
    }
    
    void SetPattern(EnhancedFluidSim.IdlePatternType pattern)
    {
        if (fluidSim != null)
        {
            fluidSim.currentPattern = pattern;
            UpdateUI();
            Debug.Log($"Idle animation pattern changed to: {pattern}");
        }
    }
    
    void SetAnimationSpeed(float speed)
    {
        if (fluidSim != null)
        {
            fluidSim.idleAnimationSpeed = speed;
        }
    }
    
    void SetIntensity(float intensity)
    {
        if (fluidSim != null)
        {
            fluidSim.idleIntensity = intensity;
        }
    }
    
    void UpdateUI()
    {
        if (fluidSim != null && currentPatternText != null)
        {
            currentPatternText.text = $"Current: {patternNames[(int)fluidSim.currentPattern]}";
        }
    }
    
    void Update()
    {
        // Update UI periodically
        if (Time.frameCount % 60 == 0) // Every 60 frames
        {
            UpdateUI();
        }
    }
}
