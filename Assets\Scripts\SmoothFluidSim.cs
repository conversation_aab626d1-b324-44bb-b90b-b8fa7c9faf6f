using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Advanced fluid simulation with smooth motion and better blending
/// </summary>
public class SmoothFluidSim : MonoBehaviour
{
    [Header("UI")]
    public RawImage targetRawImage;
    public ComputeShader smoothFluidCompute;
    
    [Header("Simulation Quality")]
    [Range(128, 512)]
    public int resolution = 256;
    [Range(1, 5)]
    public int substeps = 2;
    [Range(10, 50)]
    public int jacobiIterations = 30;
    
    [Header("Fluid Properties")]
    [Range(0.0001f, 0.01f)]
    public float dyeDissipation = 0.0002f;
    [Range(0.0001f, 0.01f)]
    public float velocityDissipation = 0.0005f;
    [Range(0.0001f, 0.01f)]
    public float dyeDiffusion = 0.001f;
    [Range(0.0f, 2.0f)]
    public float curlStrength = 0.3f;
    
    [Header("Visual")]
    [Range(0.05f, 0.3f)]
    public float splatRadius = 0.12f;
    [Range(1.0f, 5.0f)]
    public float colorIntensity = 2.5f;
    [Range(10f, 100f)]
    public float forceMultiplier = 50f;
    
    [Header("Animation")]
    public bool enableIdleAnimation = true;
    [Range(0.2f, 2.0f)]
    public float idleSplatInterval = 0.6f;
    [Range(0.5f, 3.0f)]
    public float idleForceStrength = 1.5f;
    
    // Render textures
    private RenderTexture velocityRT, velocityRT2;
    private RenderTexture dyeRT, dyeRT2;
    private RenderTexture pressureRT, divergenceRT, curlRT;
    
    // Kernel IDs
    private int kernelAdvectVel = -1;
    private int kernelAdvectDye = -1;
    private int kernelAddSplat = -1;
    private int kernelDivergence = -1;
    private int kernelJacobi = -1;
    private int kernelSubGrad = -1;
    private int kernelCurlForce = -1;
    private int kernelDiffuseDye = -1;
    
    private float lastIdleSplatTime;
    private int splatCounter = 0;
    
    void Start()
    {
        SetupSmoothFluid();
    }
    
    void SetupSmoothFluid()
    {
        if (smoothFluidCompute == null || targetRawImage == null)
        {
            Debug.LogError("SmoothFluidSim: Missing required references!");
            return;
        }
        
        if (!SystemInfo.supportsComputeShaders)
        {
            Debug.LogError("SmoothFluidSim: Compute shaders not supported!");
            return;
        }
        
        InitializeRenderTextures();
        CacheKernelIDs();
        
        // Assign to UI
        targetRawImage.texture = dyeRT;
        targetRawImage.material = null;
        targetRawImage.color = Color.white;
        
        // Add initial splats for immediate visual feedback
        AddInitialSplats();
        
        Debug.Log("SmoothFluidSim: Advanced fluid simulation initialized");
    }
    
    void InitializeRenderTextures()
    {
        // Release existing textures
        ReleaseTextures();
        
        // High precision format for better quality
        var rtDesc = new RenderTextureDescriptor(resolution, resolution)
        {
            colorFormat = RenderTextureFormat.ARGBFloat,
            enableRandomWrite = true,
            sRGB = false,
            useMipMap = false
        };
        
        // Velocity textures
        velocityRT = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear, wrapMode = TextureWrapMode.Clamp };
        velocityRT.Create();
        velocityRT2 = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear, wrapMode = TextureWrapMode.Clamp };
        velocityRT2.Create();
        
        // Dye textures
        dyeRT = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear, wrapMode = TextureWrapMode.Clamp };
        dyeRT.Create();
        dyeRT2 = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear, wrapMode = TextureWrapMode.Clamp };
        dyeRT2.Create();
        
        // Pressure and divergence (single channel)
        pressureRT = new RenderTexture(resolution, resolution, 0, RenderTextureFormat.RFloat) 
        { enableRandomWrite = true, filterMode = FilterMode.Bilinear };
        pressureRT.Create();
        
        divergenceRT = new RenderTexture(resolution, resolution, 0, RenderTextureFormat.RFloat) 
        { enableRandomWrite = true, filterMode = FilterMode.Bilinear };
        divergenceRT.Create();
        
        curlRT = new RenderTexture(resolution, resolution, 0, RenderTextureFormat.RFloat) 
        { enableRandomWrite = true, filterMode = FilterMode.Bilinear };
        curlRT.Create();
        
        // Clear all textures
        ClearTextures();
    }
    
    void ClearTextures()
    {
        Graphics.Blit(Texture2D.blackTexture, velocityRT);
        Graphics.Blit(Texture2D.blackTexture, velocityRT2);
        Graphics.Blit(Texture2D.blackTexture, dyeRT);
        Graphics.Blit(Texture2D.blackTexture, dyeRT2);
        Graphics.Blit(Texture2D.blackTexture, pressureRT);
        Graphics.Blit(Texture2D.blackTexture, divergenceRT);
        Graphics.Blit(Texture2D.blackTexture, curlRT);
    }
    
    void CacheKernelIDs()
    {
        kernelAdvectVel = smoothFluidCompute.FindKernel("AdvectVelocity");
        kernelAdvectDye = smoothFluidCompute.FindKernel("AdvectDye");
        kernelAddSplat = smoothFluidCompute.FindKernel("AddSplat");
        kernelDivergence = smoothFluidCompute.FindKernel("ComputeDivergence");
        kernelJacobi = smoothFluidCompute.FindKernel("Jacobi");
        kernelSubGrad = smoothFluidCompute.FindKernel("SubtractGradient");
        kernelCurlForce = smoothFluidCompute.FindKernel("AddCurlForce");
        kernelDiffuseDye = smoothFluidCompute.FindKernel("DiffuseDye");
        
        Debug.Log($"SmoothFluidSim: All kernels cached successfully");
    }
    
    void AddInitialSplats()
    {
        // Add diverse initial splats across the full color spectrum
        AddSplat(new Vector2(0.2f, 0.3f), Color.HSVToRGB(0.0f, 1.0f, 1.0f) * colorIntensity, new Vector2(40f, 20f));      // Red
        AddSplat(new Vector2(0.8f, 0.3f), Color.HSVToRGB(0.15f, 1.0f, 1.0f) * colorIntensity, new Vector2(-40f, 20f));    // Orange
        AddSplat(new Vector2(0.2f, 0.7f), Color.HSVToRGB(0.33f, 1.0f, 1.0f) * colorIntensity, new Vector2(40f, -20f));    // Green
        AddSplat(new Vector2(0.8f, 0.7f), Color.HSVToRGB(0.66f, 1.0f, 1.0f) * colorIntensity, new Vector2(-40f, -20f));   // Blue
        AddSplat(new Vector2(0.5f, 0.2f), Color.HSVToRGB(0.83f, 1.0f, 1.0f) * colorIntensity, new Vector2(0f, 40f));      // Purple
        AddSplat(new Vector2(0.5f, 0.8f), Color.HSVToRGB(0.5f, 1.0f, 1.0f) * colorIntensity, new Vector2(0f, -40f));      // Cyan
        AddSplat(new Vector2(0.3f, 0.5f), Color.HSVToRGB(0.25f, 1.0f, 1.0f) * colorIntensity, new Vector2(30f, 0f));      // Yellow-Green
        AddSplat(new Vector2(0.7f, 0.5f), Color.HSVToRGB(0.75f, 1.0f, 1.0f) * colorIntensity, new Vector2(-30f, 0f));     // Magenta
    }
    
    void Update()
    {
        if (dyeRT == null) return;
        
        // Handle idle animation
        if (enableIdleAnimation && Time.time - lastIdleSplatTime > idleSplatInterval)
        {
            lastIdleSplatTime = Time.time;
            AddIdleSplat();
        }
        
        // Handle user input
        HandleInput();
        
        // Run fluid simulation with substeps for better quality
        float dt = Time.deltaTime / substeps;
        for (int i = 0; i < substeps; i++)
        {
            StepFluidSimulation(dt);
        }
        
        // Ensure UI texture is up to date
        if (targetRawImage.texture != dyeRT)
            targetRawImage.texture = dyeRT;
    }
    
    void StepFluidSimulation(float deltaTime)
    {
        // Set common parameters
        smoothFluidCompute.SetInts("_SimResolution", new int[] { resolution, resolution });
        smoothFluidCompute.SetFloat("_TimeStep", deltaTime);
        smoothFluidCompute.SetFloat("_VelocityDissipation", velocityDissipation);
        smoothFluidCompute.SetFloat("_Dissipation", dyeDissipation);
        smoothFluidCompute.SetFloat("_PressureDissipation", 0.01f);
        smoothFluidCompute.SetFloat("_CurlStrength", curlStrength);
        smoothFluidCompute.SetFloat("_DyeDiffusion", dyeDiffusion);
        
        // Step 1: Advect velocity
        if (kernelAdvectVel >= 0)
        {
            smoothFluidCompute.SetTexture(kernelAdvectVel, "_Velocity", velocityRT);
            smoothFluidCompute.SetTexture(kernelAdvectVel, "_VelocityOut", velocityRT2);
            DispatchKernel(kernelAdvectVel);
            SwapTextures(ref velocityRT, ref velocityRT2);
        }
        
        // Step 2: Add curl forces for more interesting motion
        if (kernelCurlForce >= 0 && curlStrength > 0)
        {
            smoothFluidCompute.SetTexture(kernelCurlForce, "_Velocity", velocityRT);
            smoothFluidCompute.SetTexture(kernelCurlForce, "_VelocityOut", velocityRT2);
            smoothFluidCompute.SetTexture(kernelCurlForce, "_Curl", curlRT);
            DispatchKernel(kernelCurlForce);
            SwapTextures(ref velocityRT, ref velocityRT2);
        }
        
        // Step 3: Compute divergence
        if (kernelDivergence >= 0)
        {
            smoothFluidCompute.SetTexture(kernelDivergence, "_Velocity", velocityRT);
            smoothFluidCompute.SetTexture(kernelDivergence, "_Divergence", divergenceRT);
            DispatchKernel(kernelDivergence);
        }
        
        // Step 4: Clear pressure
        Graphics.Blit(Texture2D.blackTexture, pressureRT);
        
        // Step 5: Solve pressure (Jacobi iterations)
        if (kernelJacobi >= 0)
        {
            smoothFluidCompute.SetTexture(kernelJacobi, "_Divergence", divergenceRT);
            smoothFluidCompute.SetTexture(kernelJacobi, "_Pressure", pressureRT);
            
            for (int i = 0; i < jacobiIterations; i++)
            {
                DispatchKernel(kernelJacobi);
            }
        }
        
        // Step 6: Subtract pressure gradient
        if (kernelSubGrad >= 0)
        {
            smoothFluidCompute.SetTexture(kernelSubGrad, "_Pressure", pressureRT);
            smoothFluidCompute.SetTexture(kernelSubGrad, "_Velocity", velocityRT);
            smoothFluidCompute.SetTexture(kernelSubGrad, "_VelocityOut", velocityRT2);
            DispatchKernel(kernelSubGrad);
            SwapTextures(ref velocityRT, ref velocityRT2);
        }
        
        // Step 7: Advect dye
        if (kernelAdvectDye >= 0)
        {
            smoothFluidCompute.SetTexture(kernelAdvectDye, "_Velocity", velocityRT);
            smoothFluidCompute.SetTexture(kernelAdvectDye, "_Dye", dyeRT);
            smoothFluidCompute.SetTexture(kernelAdvectDye, "_DyeOut", dyeRT2);
            DispatchKernel(kernelAdvectDye);
            SwapTextures(ref dyeRT, ref dyeRT2);
        }
        
        // Step 8: Diffuse dye for better mixing
        if (kernelDiffuseDye >= 0 && dyeDiffusion > 0)
        {
            smoothFluidCompute.SetTexture(kernelDiffuseDye, "_Dye", dyeRT);
            smoothFluidCompute.SetTexture(kernelDiffuseDye, "_DyeOut", dyeRT2);
            DispatchKernel(kernelDiffuseDye);
            SwapTextures(ref dyeRT, ref dyeRT2);
        }
    }
    
    void AddIdleSplat()
    {
        splatCounter++;
        
        // Create interesting patterns
        float time = Time.time * 0.5f;
        Vector2 pos = new Vector2(
            0.3f + 0.4f * Mathf.Sin(time * 0.8f + splatCounter * 0.5f),
            0.3f + 0.4f * Mathf.Cos(time * 0.6f + splatCounter * 0.3f)
        );
        
        // Cycle through colors smoothly
        float hue = (time * 0.1f + splatCounter * 0.15f) % 1f;
        Color color = Color.HSVToRGB(hue, 0.8f, 1.0f) * colorIntensity;
        
        // Create swirling forces
        Vector2 center = new Vector2(0.5f, 0.5f);
        Vector2 toCenter = center - pos;
        Vector2 tangent = new Vector2(-toCenter.y, toCenter.x).normalized;
        Vector2 force = (tangent + toCenter.normalized * 0.3f) * forceMultiplier * idleForceStrength;
        
        AddSplat(pos, color, force);
    }
    
    void HandleInput()
    {
        if (Input.GetMouseButton(0))
        {
            Vector2 mousePos = Input.mousePosition;
            
            RectTransform rectTransform = targetRawImage.GetComponent<RectTransform>();
            Canvas canvas = targetRawImage.GetComponentInParent<Canvas>();
            Camera uiCamera = canvas.renderMode == RenderMode.ScreenSpaceOverlay ? null : canvas.worldCamera;
            
            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
                rectTransform, mousePos, uiCamera, out Vector2 localPoint))
            {
                Rect rect = rectTransform.rect;
                Vector2 uv = new Vector2(
                    (localPoint.x - rect.x) / rect.width,
                    (localPoint.y - rect.y) / rect.height
                );
                
                if (uv.x >= 0 && uv.x <= 1 && uv.y >= 0 && uv.y <= 1)
                {
                    Color color = Color.HSVToRGB((Time.time * 0.2f) % 1f, 0.9f, 1.0f) * colorIntensity;
                    Vector2 force = new Vector2(
                        Input.GetAxis("Mouse X") * forceMultiplier * 3f,
                        Input.GetAxis("Mouse Y") * forceMultiplier * 3f
                    );
                    AddSplat(uv, color, force);
                }
            }
        }
    }
    
    void AddSplat(Vector2 position, Color color, Vector2 force)
    {
        if (kernelAddSplat < 0) return;
        
        smoothFluidCompute.SetFloat("_Radius", splatRadius);
        smoothFluidCompute.SetFloats("_SplatColor", new float[] { color.r, color.g, color.b, 1.0f });
        smoothFluidCompute.SetFloats("_SplatPos", new float[] { position.x, position.y });
        smoothFluidCompute.SetFloats("_SplatForce", new float[] { force.x, force.y });
        
        smoothFluidCompute.SetTexture(kernelAddSplat, "_Dye", dyeRT);
        smoothFluidCompute.SetTexture(kernelAddSplat, "_DyeOut", dyeRT2);
        smoothFluidCompute.SetTexture(kernelAddSplat, "_Velocity", velocityRT);
        smoothFluidCompute.SetTexture(kernelAddSplat, "_VelocityOut", velocityRT2);
        
        DispatchKernel(kernelAddSplat);
        SwapTextures(ref dyeRT, ref dyeRT2);
        SwapTextures(ref velocityRT, ref velocityRT2);
    }
    
    void DispatchKernel(int kernel)
    {
        int groupX = Mathf.CeilToInt(resolution / 8.0f);
        int groupY = Mathf.CeilToInt(resolution / 8.0f);
        smoothFluidCompute.Dispatch(kernel, groupX, groupY, 1);
    }
    
    void SwapTextures(ref RenderTexture a, ref RenderTexture b)
    {
        RenderTexture temp = a;
        a = b;
        b = temp;
    }
    
    void ReleaseTextures()
    {
        if (velocityRT) { velocityRT.Release(); velocityRT = null; }
        if (velocityRT2) { velocityRT2.Release(); velocityRT2 = null; }
        if (dyeRT) { dyeRT.Release(); dyeRT = null; }
        if (dyeRT2) { dyeRT2.Release(); dyeRT2 = null; }
        if (pressureRT) { pressureRT.Release(); pressureRT = null; }
        if (divergenceRT) { divergenceRT.Release(); divergenceRT = null; }
        if (curlRT) { curlRT.Release(); curlRT = null; }
    }
    
    void OnDestroy()
    {
        ReleaseTextures();
    }
    
    void OnGUI()
    {
        GUILayout.BeginArea(new Rect(10, 10, 300, 250));
        GUILayout.Label("=== SMOOTH FLUID SIMULATION ===");
        GUILayout.Label($"Resolution: {resolution}x{resolution}");
        GUILayout.Label($"Substeps: {substeps}");
        GUILayout.Label($"Jacobi Iterations: {jacobiIterations}");
        GUILayout.Label($"Curl Strength: {curlStrength:F3}");
        GUILayout.Label($"Dye Diffusion: {dyeDiffusion:F6}");
        GUILayout.Label("Drag mouse to interact!");
        
        if (GUILayout.Button("Add Random Splat"))
        {
            AddIdleSplat();
        }
        
        if (GUILayout.Button("Reset Simulation"))
        {
            ClearTextures();
            AddInitialSplats();
        }
        
        GUILayout.EndArea();
    }
}