using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

/// <summary>
/// Utility script to automatically set up a fluid simulation scene
/// Run this in the editor or at runtime to create the complete UI setup
/// </summary>
public class FluidSceneSetup : MonoBehaviour
{
    [Header("Auto Setup")]
    [SerializeField] public bool setupOnStart = true;
    [SerializeField] private bool setupInEditor = false;
    
    [Header("References (Auto-assigned if null)")]
    public ComputeShader fluidCompute;
    public ComputeShader enhancedFluidCompute;
    public Material fluidBlitMaterial;
    public Material fluidUnlitMaterial;

    [Header("Simulation Type")]
    public bool useEnhancedFluid = true;
    
    void Start()
    {
        if (setupOnStart)
        {
            SetupFluidScene();
        }
    }
    
    void OnValidate()
    {
        if (setupInEditor && Application.isPlaying)
        {
            SetupFluidScene();
        }
    }
    
    [ContextMenu("Setup Fluid Scene")]
    public void SetupFluidScene()
    {
        // Auto-find resources if not assigned
        if (fluidCompute == null)
        {
            fluidCompute = Resources.Load<ComputeShader>("Fluid") ??
                          FindAssetByName<ComputeShader>("Fluid");
        }

        if (enhancedFluidCompute == null)
        {
            enhancedFluidCompute = Resources.Load<ComputeShader>("EnhancedFluid") ??
                                  FindAssetByName<ComputeShader>("EnhancedFluid");
        }
        
        if (fluidBlitMaterial == null)
        {
            fluidBlitMaterial = FindAssetByName<Material>("FluidBlit") ?? 
                               CreateFluidBlitMaterial();
        }
        
        if (fluidUnlitMaterial == null)
        {
            fluidUnlitMaterial = FindAssetByName<Material>("FluidUnlit") ?? 
                                CreateFluidUnlitMaterial();
        }
        
        // Create Canvas if it doesn't exist
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            GameObject canvasGO = new GameObject("Canvas");
            canvas = canvasGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasGO.AddComponent<CanvasScaler>();
            canvasGO.AddComponent<GraphicRaycaster>();
        }
        
        // Create EventSystem if it doesn't exist
        if (FindObjectOfType<EventSystem>() == null)
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystemGO.AddComponent<EventSystem>();
            eventSystemGO.AddComponent<StandaloneInputModule>();
        }
        
        // Create main UI panel
        GameObject panelGO = new GameObject("FluidPanel");
        panelGO.transform.SetParent(canvas.transform, false);
        
        Image panelImage = panelGO.AddComponent<Image>();
        panelImage.color = new Color(0.1f, 0.1f, 0.1f, 1f); // Dark background
        
        RectTransform panelRect = panelGO.GetComponent<RectTransform>();
        panelRect.anchorMin = Vector2.zero;
        panelRect.anchorMax = Vector2.one;
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // Create Raw Image for fluid display
        GameObject rawImageGO = new GameObject("FluidRawImage");
        rawImageGO.transform.SetParent(panelGO.transform, false);
        
        RawImage rawImage = rawImageGO.AddComponent<RawImage>();
        rawImage.color = Color.white;
        
        RectTransform rawImageRect = rawImageGO.GetComponent<RectTransform>();
        rawImageRect.anchorMin = new Vector2(0.5f, 0.5f);
        rawImageRect.anchorMax = new Vector2(0.5f, 0.5f);
        rawImageRect.anchoredPosition = Vector2.zero;
        rawImageRect.sizeDelta = new Vector2(512, 512); // Square aspect ratio
        
        // Create title text
        GameObject titleGO = new GameObject("Title");
        titleGO.transform.SetParent(panelGO.transform, false);
        
        Text titleText = titleGO.AddComponent<Text>();
        titleText.text = "Unity Fluid Simulation - Click/Touch to Interact";
        titleText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        titleText.fontSize = 24;
        titleText.color = Color.white;
        titleText.alignment = TextAnchor.MiddleCenter;
        
        RectTransform titleRect = titleGO.GetComponent<RectTransform>();
        titleRect.anchorMin = new Vector2(0f, 1f);
        titleRect.anchorMax = new Vector2(1f, 1f);
        titleRect.anchoredPosition = new Vector2(0, -30);
        titleRect.sizeDelta = new Vector2(0, 60);
        
        // Create instructions text
        GameObject instructionsGO = new GameObject("Instructions");
        instructionsGO.transform.SetParent(panelGO.transform, false);
        
        Text instructionsText = instructionsGO.AddComponent<Text>();
        instructionsText.text = "This fluid simulation runs entirely on the GPU using compute shaders.\n" +
                               "It features automatic idle animation with periodic color splats.\n" +
                               "Click or touch anywhere on the fluid to add interactive splats!";
        instructionsText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        instructionsText.fontSize = 16;
        instructionsText.color = new Color(0.8f, 0.8f, 0.8f, 1f);
        instructionsText.alignment = TextAnchor.MiddleCenter;
        
        RectTransform instructionsRect = instructionsGO.GetComponent<RectTransform>();
        instructionsRect.anchorMin = new Vector2(0f, 0f);
        instructionsRect.anchorMax = new Vector2(1f, 0f);
        instructionsRect.anchoredPosition = new Vector2(0, 60);
        instructionsRect.sizeDelta = new Vector2(-40, 120);
        
        // Choose simulation type based on settings
        if (useEnhancedFluid && enhancedFluidCompute != null)
        {
            // Use enhanced fluid simulation with advanced idle animation
            EnhancedFluidSim enhancedFluidSim = panelGO.GetComponent<EnhancedFluidSim>();
            if (enhancedFluidSim == null)
            {
                enhancedFluidSim = panelGO.AddComponent<EnhancedFluidSim>();
            }

            // Configure enhanced fluid simulation
            enhancedFluidSim.enhancedFluidCompute = enhancedFluidCompute;
            enhancedFluidSim.targetImage = rawImage;
            enhancedFluidSim.blitMaterial = fluidBlitMaterial;
            enhancedFluidSim.simResolution = 256;
            enhancedFluidSim.dyeDissipation = 0.001f;
            enhancedFluidSim.velocityDissipation = 0.005f;
            enhancedFluidSim.pressureDissipation = 0.01f;
            enhancedFluidSim.enableIdleAnimation = true;
            enhancedFluidSim.idleAnimationSpeed = 1.0f;
            enhancedFluidSim.idleIntensity = 1.5f;
            enhancedFluidSim.currentPattern = EnhancedFluidSim.IdlePatternType.Cycling;
            enhancedFluidSim.jacobiIterations = 20;

            Debug.Log("Enhanced Fluid Simulation configured with advanced idle animation patterns!");
        }
        else
        {
            // Fallback to original FluidSim
            FluidSim fluidSim = panelGO.GetComponent<FluidSim>();
            if (fluidSim == null)
            {
                fluidSim = panelGO.AddComponent<FluidSim>();
            }

            // Configure for proper fluid behavior with good color mixing
            fluidSim.targetImage = rawImage;
            fluidSim.fluidCompute = fluidCompute;
            fluidSim.simResolution = 256;
            fluidSim.dyeDissipation = 0.0001f;       // Low dissipation for persistent colors
            fluidSim.velocityDissipation = 0.005f;   // Moderate velocity dissipation for fluid motion
            fluidSim.splatRadius = 0.08f;            // Proper splat size for fluid behavior
            fluidSim.idleSplatInterval = 0.5f;       // Reasonable splat frequency
            fluidSim.idleSplatIntensity = 1.5f;      // Moderate intensity for proper mixing
            fluidSim.enableIdleAnimation = true;
            fluidSim.jacobiIterations = 20;          // Standard iterations for good performance
            fluidSim.blitMaterial = fluidBlitMaterial;

            Debug.Log("Standard Fluid Simulation configured with basic idle animation!");
        }
        
        Debug.Log("Fluid Scene Setup Complete! The fluid simulation should now be running on the Raw Image.");
        Debug.Log($"Resources found - Compute: {fluidCompute != null}, Enhanced: {enhancedFluidCompute != null}, BlitMat: {fluidBlitMaterial != null}, UnlitMat: {fluidUnlitMaterial != null}");
    }
    
    private T FindAssetByName<T>(string name) where T : Object
    {
        // Try to find asset in the project
#if UNITY_EDITOR
        string[] guids = UnityEditor.AssetDatabase.FindAssets($"{name} t:{typeof(T).Name}");
        if (guids.Length > 0)
        {
            string path = UnityEditor.AssetDatabase.GUIDToAssetPath(guids[0]);
            return UnityEditor.AssetDatabase.LoadAssetAtPath<T>(path);
        }
#endif
        return null;
    }
    
    private Material CreateFluidBlitMaterial()
    {
        Shader shader = Shader.Find("Hidden/FluidBlit");
        if (shader != null)
        {
            return new Material(shader);
        }
        return null;
    }
    
    private Material CreateFluidUnlitMaterial()
    {
        // Try the new UI shader first
        Shader shader = Shader.Find("UI/FluidDisplay");
        if (shader != null)
        {
            Material mat = new Material(shader);
            // Set some good default values for fluid display
            mat.SetFloat("_Brightness", 1.5f);
            mat.SetFloat("_Contrast", 1.2f);
            mat.SetFloat("_Saturation", 1.3f);
            mat.SetFloat("_ColorBoost", 2.0f);
            return mat;
        }
        
        // Fallback to watercolor shader
        shader = Shader.Find("Custom/FluidWatercolorUnlit");
        if (shader != null)
        {
            return new Material(shader);
        }
        
        // Final fallback to UI default
        shader = Shader.Find("UI/Default");
        if (shader != null)
        {
            return new Material(shader);
        }
        
        return null;
    }
}