using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Test the improved compute shader with proper fluid simulation steps
/// </summary>
public class ImprovedFluidTest : MonoBehaviour
{
    [Header("Settings")]
    public RawImage testRawImage;
    public ComputeShader fluidCompute;
    
    [Header("Simulation")]
    public int resolution = 256;
    public float dyeDissipation = 0.0001f;
    public float velocityDissipation = 0.001f;
    public float splatRadius = 0.15f;
    public int jacobiIterations = 20;
    
    [Header("Debug")]
    public bool showDebugInfo = true;
    public bool autoAddSplats = true;
    public float splatInterval = 0.8f;
    
    private RenderTexture velocityRT, velocityRT2;
    private RenderTexture dyeRT, dyeRT2;
    private RenderTexture pressureRT, divergenceRT;
    
    private int kernelAdvectVel = -1;
    private int kernelAdvectDye = -1;
    private int kernelAddSplat = -1;
    private int kernelDivergence = -1;
    private int kernelJacobi = -1;
    private int kernelSubGrad = -1;
    
    private float lastSplatTime;
    
    void Start()
    {
        SetupImprovedFluidTest();
    }
    
    void SetupImprovedFluidTest()
    {
        if (fluidCompute == null || testRawImage == null)
        {
            Debug.LogError("ImprovedFluidTest: Missing required references!");
            return;
        }
        
        if (!SystemInfo.supportsComputeShaders)
        {
            Debug.LogError("ImprovedFluidTest: Compute shaders not supported!");
            return;
        }
        
        // Initialize render textures
        InitRenderTextures();
        
        // Cache kernel IDs
        CacheKernels();
        
        // Assign to Raw Image
        testRawImage.texture = dyeRT;
        testRawImage.material = null;
        testRawImage.color = Color.white;
        
        // Add initial splats
        AddTestSplat(new Vector2(0.3f, 0.5f), Color.red, new Vector2(30f, 10f));
        AddTestSplat(new Vector2(0.7f, 0.5f), Color.blue, new Vector2(-30f, -10f));
        AddTestSplat(new Vector2(0.5f, 0.3f), Color.green, new Vector2(10f, 30f));
        
        Debug.Log("ImprovedFluidTest: Setup complete with proper fluid simulation");
    }
    
    void InitRenderTextures()
    {
        var rtDesc = new RenderTextureDescriptor(resolution, resolution)
        {
            colorFormat = RenderTextureFormat.ARGBFloat,
            enableRandomWrite = true,
            sRGB = false
        };
        
        velocityRT = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear };
        velocityRT.Create();
        velocityRT2 = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear };
        velocityRT2.Create();
        
        dyeRT = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear };
        dyeRT.Create();
        dyeRT2 = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear };
        dyeRT2.Create();
        
        pressureRT = new RenderTexture(resolution, resolution, 0, RenderTextureFormat.RFloat) 
        { enableRandomWrite = true, filterMode = FilterMode.Bilinear };
        pressureRT.Create();
        
        divergenceRT = new RenderTexture(resolution, resolution, 0, RenderTextureFormat.RFloat) 
        { enableRandomWrite = true, filterMode = FilterMode.Bilinear };
        divergenceRT.Create();
        
        // Clear all textures
        Graphics.Blit(Texture2D.blackTexture, velocityRT);
        Graphics.Blit(Texture2D.blackTexture, velocityRT2);
        Graphics.Blit(Texture2D.blackTexture, dyeRT);
        Graphics.Blit(Texture2D.blackTexture, dyeRT2);
        Graphics.Blit(Texture2D.blackTexture, pressureRT);
        Graphics.Blit(Texture2D.blackTexture, divergenceRT);
    }
    
    void CacheKernels()
    {
        kernelAdvectVel = fluidCompute.FindKernel("AdvectVelocity");
        kernelAdvectDye = fluidCompute.FindKernel("AdvectDye");
        kernelAddSplat = fluidCompute.FindKernel("AddSplat");
        kernelDivergence = fluidCompute.FindKernel("ComputeDivergence");
        kernelJacobi = fluidCompute.FindKernel("Jacobi");
        kernelSubGrad = fluidCompute.FindKernel("SubtractGradient");
        
        Debug.Log($"ImprovedFluidTest: Kernels cached - AdvectVel:{kernelAdvectVel}, AdvectDye:{kernelAdvectDye}, AddSplat:{kernelAddSplat}");
    }
    
    void Update()
    {
        if (dyeRT == null) return;
        
        // Auto add splats
        if (autoAddSplats && Time.time - lastSplatTime > splatInterval)
        {
            lastSplatTime = Time.time;
            AddRandomSplat();
        }
        
        // Handle mouse input
        if (Input.GetMouseButtonDown(0))
        {
            HandleMouseInput();
        }
        
        // Run fluid simulation
        StepFluidSimulation();
    }
    
    void StepFluidSimulation()
    {
        float dt = Time.deltaTime;
        
        // Set common parameters
        fluidCompute.SetInts("_SimResolution", new int[] { resolution, resolution });
        fluidCompute.SetFloat("_TimeStep", dt);
        
        // Step 1: Advect velocity
        if (kernelAdvectVel >= 0)
        {
            fluidCompute.SetFloat("_VelocityDissipation", velocityDissipation);
            fluidCompute.SetTexture(kernelAdvectVel, "_Velocity", velocityRT);
            fluidCompute.SetTexture(kernelAdvectVel, "_VelocityOut", velocityRT2);
            DispatchKernel(kernelAdvectVel);
            SwapTextures(ref velocityRT, ref velocityRT2);
        }
        
        // Step 2: Advect dye
        if (kernelAdvectDye >= 0)
        {
            fluidCompute.SetFloat("_Dissipation", dyeDissipation);
            fluidCompute.SetTexture(kernelAdvectDye, "_Velocity", velocityRT);
            fluidCompute.SetTexture(kernelAdvectDye, "_Dye", dyeRT);
            fluidCompute.SetTexture(kernelAdvectDye, "_DyeOut", dyeRT2);
            DispatchKernel(kernelAdvectDye);
            SwapTextures(ref dyeRT, ref dyeRT2);
        }
        
        // Step 3: Compute divergence
        if (kernelDivergence >= 0)
        {
            fluidCompute.SetTexture(kernelDivergence, "_Velocity", velocityRT);
            fluidCompute.SetTexture(kernelDivergence, "_Divergence", divergenceRT);
            DispatchKernel(kernelDivergence);
        }
        
        // Step 4: Clear pressure
        Graphics.Blit(Texture2D.blackTexture, pressureRT);
        
        // Step 5: Jacobi iterations for pressure
        if (kernelJacobi >= 0)
        {
            fluidCompute.SetTexture(kernelJacobi, "_Divergence", divergenceRT);
            fluidCompute.SetTexture(kernelJacobi, "_Pressure", pressureRT);
            fluidCompute.SetFloat("_PressureDissipation", 0.01f);
            
            for (int i = 0; i < jacobiIterations; i++)
            {
                DispatchKernel(kernelJacobi);
            }
        }
        
        // Step 6: Subtract pressure gradient
        if (kernelSubGrad >= 0)
        {
            fluidCompute.SetTexture(kernelSubGrad, "_Pressure", pressureRT);
            fluidCompute.SetTexture(kernelSubGrad, "_Velocity", velocityRT);
            fluidCompute.SetTexture(kernelSubGrad, "_VelocityOut", velocityRT2);
            DispatchKernel(kernelSubGrad);
            SwapTextures(ref velocityRT, ref velocityRT2);
        }
        
        // Step 7: Advect dye again for more fluid motion
        if (kernelAdvectDye >= 0)
        {
            fluidCompute.SetTexture(kernelAdvectDye, "_Velocity", velocityRT);
            fluidCompute.SetTexture(kernelAdvectDye, "_Dye", dyeRT);
            fluidCompute.SetTexture(kernelAdvectDye, "_DyeOut", dyeRT2);
            DispatchKernel(kernelAdvectDye);
            SwapTextures(ref dyeRT, ref dyeRT2);
        }
        
        // Update Raw Image texture
        if (testRawImage.texture != dyeRT)
            testRawImage.texture = dyeRT;
    }
    
    void AddRandomSplat()
    {
        Vector2 pos = new Vector2(Random.Range(0.2f, 0.8f), Random.Range(0.2f, 0.8f));
        Color color = Color.HSVToRGB(Random.value, 0.8f, 1.0f) * 2.0f;
        Vector2 force = new Vector2(Random.Range(-40f, 40f), Random.Range(-40f, 40f));
        
        AddTestSplat(pos, color, force);
    }
    
    void HandleMouseInput()
    {
        Vector2 mousePos = Input.mousePosition;
        
        RectTransform rectTransform = testRawImage.GetComponent<RectTransform>();
        Canvas canvas = testRawImage.GetComponentInParent<Canvas>();
        Camera uiCamera = canvas.renderMode == RenderMode.ScreenSpaceOverlay ? null : canvas.worldCamera;
        
        if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
            rectTransform, mousePos, uiCamera, out Vector2 localPoint))
        {
            Rect rect = rectTransform.rect;
            Vector2 uv = new Vector2(
                (localPoint.x - rect.x) / rect.width,
                (localPoint.y - rect.y) / rect.height
            );
            
            if (uv.x >= 0 && uv.x <= 1 && uv.y >= 0 && uv.y <= 1)
            {
                Color color = Color.HSVToRGB(Time.time * 0.1f % 1f, 0.8f, 1.0f) * 3.0f;
                Vector2 force = new Vector2(
                    Input.GetAxis("Mouse X") * 200f,
                    Input.GetAxis("Mouse Y") * 200f
                );
                AddTestSplat(uv, color, force);
            }
        }
    }
    
    void AddTestSplat(Vector2 uv, Color color, Vector2 force)
    {
        if (kernelAddSplat < 0) return;
        
        fluidCompute.SetFloat("_Radius", splatRadius);
        fluidCompute.SetFloats("_SplatColor", new float[] { color.r, color.g, color.b, 1.0f });
        fluidCompute.SetFloats("_SplatPos", new float[] { uv.x, uv.y });
        fluidCompute.SetFloats("_SplatForce", new float[] { force.x, force.y });
        
        fluidCompute.SetTexture(kernelAddSplat, "_Dye", dyeRT);
        fluidCompute.SetTexture(kernelAddSplat, "_DyeOut", dyeRT2);
        fluidCompute.SetTexture(kernelAddSplat, "_Velocity", velocityRT);
        fluidCompute.SetTexture(kernelAddSplat, "_VelocityOut", velocityRT2);
        
        DispatchKernel(kernelAddSplat);
        SwapTextures(ref dyeRT, ref dyeRT2);
        SwapTextures(ref velocityRT, ref velocityRT2);
    }
    
    void DispatchKernel(int kernel)
    {
        int groupX = Mathf.CeilToInt(resolution / 8.0f);
        int groupY = Mathf.CeilToInt(resolution / 8.0f);
        fluidCompute.Dispatch(kernel, groupX, groupY, 1);
    }
    
    void SwapTextures(ref RenderTexture a, ref RenderTexture b)
    {
        RenderTexture temp = a;
        a = b;
        b = temp;
    }
    
    void OnGUI()
    {
        if (!showDebugInfo) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("=== IMPROVED FLUID TEST ===");
        GUILayout.Label($"Resolution: {resolution}x{resolution}");
        GUILayout.Label($"Dye Dissipation: {dyeDissipation:F6}");
        GUILayout.Label($"Velocity Dissipation: {velocityDissipation:F6}");
        GUILayout.Label($"Jacobi Iterations: {jacobiIterations}");
        GUILayout.Label("Click to add interactive splats!");
        
        if (GUILayout.Button("Add Random Splat"))
        {
            AddRandomSplat();
        }
        
        GUILayout.EndArea();
    }
    
    void OnDestroy()
    {
        if (velocityRT) velocityRT.Release();
        if (velocityRT2) velocityRT2.Release();
        if (dyeRT) dyeRT.Release();
        if (dyeRT2) dyeRT2.Release();
        if (pressureRT) pressureRT.Release();
        if (divergenceRT) divergenceRT.Release();
    }
}