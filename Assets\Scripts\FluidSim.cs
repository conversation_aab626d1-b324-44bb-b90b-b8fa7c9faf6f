using UnityEngine;
using UnityEngine.UI;
using System.Collections.Generic;

[RequireComponent(typeof(Camera))]
public class FluidSim : MonoBehaviour
{
    [Header("Simulation")]
    public int simResolution = 256;
    public ComputeShader fluidCompute;
    public int jacobiIterations = 20;
    public bool reinitOnResolutionChange = true;
    public RawImage targetImage;
    public Material blitMaterial;

    public float timeStep = 0.016f;
    public float velocityDissipation = 0.005f;  // Even lower velocity dissipation
    public float dyeDissipation = 0.0001f;     // Extremely low dye dissipation
    public float pressureDissipation = 0.01f;

    public float splatRadius = 0.08f;  // Proper splat radius for fluid behavior
    
    [Header("Idle Animation")]
    public float idleSplatInterval = 0.5f;   // Reasonable splat frequency
    public float idleSplatIntensity = 1.5f;  // Moderate intensity for proper mixing
    public bool enableIdleAnimation = true;  // Toggle for idle animation

    [Header("Advanced Idle Animation")]
    public bool useAdvancedPatterns = true;  // Use sophisticated animation patterns
    public float patternScale = 1.0f;        // Scale of animation patterns
    public float colorCycleSpeed = 0.06f;    // Speed of color cycling
    public int splatsPerInterval = 2;        // Number of splats per interval

    [Header("Animation Patterns")]
    public bool enableSpiralPattern = true;   // Spiral motion pattern
    public bool enableWavePattern = true;     // Wave-like motion
    public bool enableOrbitPattern = true;    // Orbital motion around center
    public float spiralSpeed = 0.3f;         // Speed of spiral rotation
    public float waveAmplitude = 0.2f;       // Amplitude of wave motion
    public float orbitRadius = 0.3f;         // Radius of orbital motion

    RenderTexture velocityRT;
    RenderTexture velocityRT2;
    RenderTexture dyeRT;
    RenderTexture dyeRT2;
    RenderTexture pressureRT;
    RenderTexture divergenceRT;

    int kernelAdvectVel = -1;
    int kernelAdvectDye = -1;
    int kernelAddSplat = -1;
    int kernelDivergence = -1;
    int kernelJacobi = -1;
    int kernelSubGrad = -1;

    Camera cam;
    int currentSimResolution = -1;
    Dictionary<int, (uint x, uint y, uint z)> kernelThreadSizes = new Dictionary<int, (uint, uint, uint)>();

    void Start()
    {
        cam = GetComponent<Camera>();
        if (fluidCompute == null)
        {
            Debug.LogError("FluidSim: No ComputeShader assigned to fluidCompute. Simulation disabled.");
            enabled = false;
            return;
        }

        if (!SystemInfo.supportsComputeShaders)
        {
            Debug.LogError("FluidSim: Compute shaders are not supported on this system. Simulation disabled.");
            enabled = false;
            return;
        }

        InitRTs();
        CacheKernels();

        // Add diverse initial splats across the full color spectrum
        AddIdleSplat(new Vector2(0.2f, 0.3f), Color.HSVToRGB(0.0f, 1.0f, 1.0f) * idleSplatIntensity, new Vector2(40f, 20f));      // Red
        AddIdleSplat(new Vector2(0.8f, 0.3f), Color.HSVToRGB(0.15f, 1.0f, 1.0f) * idleSplatIntensity, new Vector2(-40f, 20f));    // Orange
        AddIdleSplat(new Vector2(0.2f, 0.7f), Color.HSVToRGB(0.33f, 1.0f, 1.0f) * idleSplatIntensity, new Vector2(40f, -20f));    // Green
        AddIdleSplat(new Vector2(0.8f, 0.7f), Color.HSVToRGB(0.66f, 1.0f, 1.0f) * idleSplatIntensity, new Vector2(-40f, -20f));   // Blue
        AddIdleSplat(new Vector2(0.5f, 0.2f), Color.HSVToRGB(0.83f, 1.0f, 1.0f) * idleSplatIntensity, new Vector2(0f, 40f));      // Purple
        AddIdleSplat(new Vector2(0.5f, 0.8f), Color.HSVToRGB(0.5f, 1.0f, 1.0f) * idleSplatIntensity, new Vector2(0f, -40f));      // Cyan
        AddIdleSplat(new Vector2(0.3f, 0.5f), Color.HSVToRGB(0.25f, 1.0f, 1.0f) * idleSplatIntensity, new Vector2(30f, 0f));      // Yellow-Green
        AddIdleSplat(new Vector2(0.7f, 0.5f), Color.HSVToRGB(0.75f, 1.0f, 1.0f) * idleSplatIntensity, new Vector2(-30f, 0f));     // Magenta

        // assign dyeRT to UI if available immediately
        if (targetImage != null && dyeRT != null)
        {
            targetImage.texture = dyeRT;
            if (blitMaterial != null) targetImage.material = blitMaterial; else targetImage.material = null;
        }
    }

    void InitRTs()
    {
        ReleaseRTs();
        int w = simResolution;
        int h = simResolution;
        currentSimResolution = simResolution;

        var rtDesc = new RenderTextureDescriptor(w, h)
        {
            colorFormat = RenderTextureFormat.ARGBFloat,
            dimension = UnityEngine.Rendering.TextureDimension.Tex2D,
            volumeDepth = 1,
            msaaSamples = 1,
            enableRandomWrite = true,
            sRGB = false  // Disable sRGB for compute shader compatibility
        };

        velocityRT = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear, wrapMode = TextureWrapMode.Clamp };
        velocityRT.Create();
        velocityRT2 = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear, wrapMode = TextureWrapMode.Clamp };
        velocityRT2.Create();

        // dye textures - keep float precision but set sensible flags for UI
        dyeRT = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear, wrapMode = TextureWrapMode.Clamp, useMipMap = false };
        dyeRT.Create();
        dyeRT2 = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear, wrapMode = TextureWrapMode.Clamp, useMipMap = false };
        dyeRT2.Create();

        pressureRT = new RenderTexture(w, h, 0, RenderTextureFormat.RFloat) { enableRandomWrite = true, filterMode = FilterMode.Bilinear };
        pressureRT.Create();
        divergenceRT = new RenderTexture(w, h, 0, RenderTextureFormat.RFloat) { enableRandomWrite = true, filterMode = FilterMode.Bilinear };
        divergenceRT.Create();

        // clear RTs to black / zero
        Graphics.Blit(Texture2D.blackTexture, velocityRT);
        Graphics.Blit(Texture2D.blackTexture, velocityRT2);
        Graphics.Blit(Texture2D.blackTexture, dyeRT);
        Graphics.Blit(Texture2D.blackTexture, dyeRT2);
        Graphics.Blit(Texture2D.blackTexture, pressureRT);
        Graphics.Blit(Texture2D.blackTexture, divergenceRT);
    }

    void ReleaseRTs()
    {
        if (velocityRT) { velocityRT.Release(); velocityRT = null; }
        if (velocityRT2) { velocityRT2.Release(); velocityRT2 = null; }
        if (dyeRT) { dyeRT.Release(); dyeRT = null; }
        if (dyeRT2) { dyeRT2.Release(); dyeRT2 = null; }
        if (pressureRT) { pressureRT.Release(); pressureRT = null; }
        if (divergenceRT) { divergenceRT.Release(); divergenceRT = null; }
    }

    void CacheKernels()
    {
        kernelThreadSizes.Clear();
        kernelAdvectVel = TryFindKernel("AdvectVelocity");
        kernelAdvectDye = TryFindKernel("AdvectDye");
        kernelAddSplat = TryFindKernel("AddSplat");
        kernelDivergence = TryFindKernel("ComputeDivergence");
        kernelJacobi = TryFindKernel("Jacobi");
        kernelSubGrad = TryFindKernel("SubtractGradient");
        Debug.Log($"FluidSim: kernel IDs -> AdvectVel:{kernelAdvectVel}, AdvectDye:{kernelAdvectDye}, AddSplat:{kernelAddSplat}, Divergence:{kernelDivergence}, Jacobi:{kernelJacobi}, SubGrad:{kernelSubGrad}");
    }

    int TryFindKernel(string name)
    {
        try
        {
            int k = fluidCompute.FindKernel(name);
            fluidCompute.GetKernelThreadGroupSizes(k, out uint tx, out uint ty, out uint tz);
            kernelThreadSizes[k] = (tx, ty, tz);
            return k;
        }
        catch
        {
            Debug.LogWarning($"Compute shader missing {name} kernel.");
            return -1;
        }
    }

    void Update()
    {
        if (reinitOnResolutionChange && simResolution != currentSimResolution)
        {
            InitRTs();
            // reassign UI texture after reinit
            if (targetImage != null && dyeRT != null) targetImage.texture = dyeRT;
        }
        HandleInputSplat();
        StepSimulation(timeStep);
    }

    void StepSimulation(float dt)
    {
        if (fluidCompute == null)
            return;

        int w = simResolution;
        int h = simResolution;

        // Advect velocity
        if (kernelAdvectVel >= 0)
        {
            fluidCompute.SetInts("_SimResolution", new int[] { w, h });
            fluidCompute.SetFloat("_TimeStep", dt);
            fluidCompute.SetFloat("_VelocityDissipation", velocityDissipation);
            fluidCompute.SetTexture(kernelAdvectVel, "_Velocity", velocityRT);
            fluidCompute.SetTexture(kernelAdvectVel, "_VelocityOut", velocityRT2);
            DispatchForKernel(kernelAdvectVel, w, h);
            Swap(ref velocityRT, ref velocityRT2);
        }

        // Advect dye
        if (kernelAdvectDye >= 0)
        {
            fluidCompute.SetFloat("_Dissipation", dyeDissipation);
            fluidCompute.SetTexture(kernelAdvectDye, "_Velocity", velocityRT);
            fluidCompute.SetTexture(kernelAdvectDye, "_Dye", dyeRT);
            fluidCompute.SetTexture(kernelAdvectDye, "_DyeOut", dyeRT2);
            DispatchForKernel(kernelAdvectDye, w, h);
            Swap(ref dyeRT, ref dyeRT2);
        }

        // compute divergence
        if (kernelDivergence >= 0)
        {
            fluidCompute.SetTexture(kernelDivergence, "_Velocity", velocityRT);
            fluidCompute.SetTexture(kernelDivergence, "_Divergence", divergenceRT);
            DispatchForKernel(kernelDivergence, w, h);
        }

        // clear pressure
        Graphics.Blit(Texture2D.blackTexture, pressureRT);

        // Jacobi iterations
        if (kernelJacobi >= 0)
        {
            fluidCompute.SetTexture(kernelJacobi, "_Divergence", divergenceRT);
            fluidCompute.SetTexture(kernelJacobi, "_Pressure", pressureRT);
            fluidCompute.SetFloat("_PressureDissipation", pressureDissipation);
            for (int i = 0; i < jacobiIterations; i++)
            {
                DispatchForKernel(kernelJacobi, w, h);
            }
        }

        // subtract gradient
        if (kernelSubGrad >= 0)
        {
            fluidCompute.SetTexture(kernelSubGrad, "_Pressure", pressureRT);
            fluidCompute.SetTexture(kernelSubGrad, "_Velocity", velocityRT);
            fluidCompute.SetTexture(kernelSubGrad, "_VelocityOut", velocityRT2);
            DispatchForKernel(kernelSubGrad, w, h);
            Swap(ref velocityRT, ref velocityRT2);
        }

        // periodic idle splats
        IdleSplatRoutine();

        // final dye advect again for more motion
        if (kernelAdvectDye >= 0)
        {
            fluidCompute.SetTexture(kernelAdvectDye, "_Velocity", velocityRT);
            fluidCompute.SetTexture(kernelAdvectDye, "_Dye", dyeRT);
            fluidCompute.SetTexture(kernelAdvectDye, "_DyeOut", dyeRT2);
            DispatchForKernel(kernelAdvectDye, w, h);
            Swap(ref dyeRT, ref dyeRT2);
        }

        // blit dye to UI RawImage via material
        BlitToUI();
    }

    void BlitToUI()
    {
        if (targetImage == null || dyeRT == null) return;

        // assign RenderTexture directly to RawImage for best performance
        if (targetImage.texture != dyeRT)
            targetImage.texture = dyeRT;

        if (blitMaterial != null)
            targetImage.material = blitMaterial;
        else
            targetImage.material = null;
    }

    // Enhanced idle animation system with multiple patterns
    float idleTimer = 0f;
    int patternCounter = 0;

    void IdleSplatRoutine()
    {
        if (!enableIdleAnimation) return;

        idleTimer += Time.deltaTime;
        if (idleTimer > idleSplatInterval)
        {
            idleTimer = 0f;
            patternCounter++;

            if (useAdvancedPatterns)
            {
                CreateAdvancedIdleAnimation();
            }
            else
            {
                CreateBasicIdleAnimation();
            }
        }
    }

    void CreateBasicIdleAnimation()
    {
        // Original simple animation
        for (int i = 0; i < splatsPerInterval; i++)
        {
            Vector2 pos = new Vector2(
                0.1f + 0.8f * Mathf.PerlinNoise(Time.time * 0.12f + i, 0.0f),
                0.1f + 0.8f * Mathf.PerlinNoise(0.0f, Time.time * 0.11f + i)
            );
            Color col = Color.HSVToRGB(Mathf.Repeat(Time.time * colorCycleSpeed + i * 0.3f, 1f), 0.8f, 1.0f);
            col *= idleSplatIntensity;
            Vector2 force = new Vector2(
                Mathf.Sin(Time.time * 0.7f + i) * 15f,
                Mathf.Cos(Time.time * 0.9f + i) * 15f
            );
            AddIdleSplat(pos, col, force);
        }
    }

    void CreateAdvancedIdleAnimation()
    {
        float time = Time.time * patternScale;

        for (int i = 0; i < splatsPerInterval; i++)
        {
            Vector2 pos = Vector2.zero;
            Vector2 force = Vector2.zero;
            Color color = Color.white;

            // Determine which pattern to use based on time and settings
            int patternType = (patternCounter + i) % GetActivePatternCount();

            switch (patternType)
            {
                case 0:
                    if (enableSpiralPattern)
                        (pos, force, color) = CreateSpiralPattern(time, i);
                    break;
                case 1:
                    if (enableWavePattern)
                        (pos, force, color) = CreateWavePattern(time, i);
                    break;
                case 2:
                    if (enableOrbitPattern)
                        (pos, force, color) = CreateOrbitPattern(time, i);
                    break;
            }

            AddIdleSplat(pos, color, force);
        }
    }

    int GetActivePatternCount()
    {
        int count = 0;
        if (enableSpiralPattern) count++;
        if (enableWavePattern) count++;
        if (enableOrbitPattern) count++;
        return Mathf.Max(1, count); // Ensure at least 1 pattern is active
    }

    (Vector2 pos, Vector2 force, Color color) CreateSpiralPattern(float time, int index)
    {
        float angle = time * spiralSpeed + index * Mathf.PI * 0.5f;
        float radius = 0.1f + 0.3f * Mathf.Sin(time * 0.3f + index);

        Vector2 pos = new Vector2(
            0.5f + radius * Mathf.Cos(angle),
            0.5f + radius * Mathf.Sin(angle)
        );

        // Tangential force for spiral motion
        Vector2 force = new Vector2(-Mathf.Sin(angle), Mathf.Cos(angle)) * 25f;

        // Color based on angle and time
        float hue = Mathf.Repeat(angle / (2f * Mathf.PI) + time * colorCycleSpeed, 1f);
        Color color = Color.HSVToRGB(hue, 0.9f, 1.0f) * idleSplatIntensity;

        return (pos, force, color);
    }

    (Vector2 pos, Vector2 force, Color color) CreateWavePattern(float time, int index)
    {
        float x = 0.1f + 0.8f * ((float)index / splatsPerInterval);
        float y = 0.5f + waveAmplitude * Mathf.Sin(time * 2f + x * 8f);

        Vector2 pos = new Vector2(x, y);

        // Force follows wave direction
        float waveDerivative = waveAmplitude * 8f * Mathf.Cos(time * 2f + x * 8f);
        Vector2 force = new Vector2(20f, waveDerivative * 30f);

        // Color based on wave position
        float hue = Mathf.Repeat(x + time * colorCycleSpeed, 1f);
        Color color = Color.HSVToRGB(hue, 0.8f, 1.0f) * idleSplatIntensity;

        return (pos, force, color);
    }

    (Vector2 pos, Vector2 force, Color color) CreateOrbitPattern(float time, int index)
    {
        float angle = time * 0.8f + index * Mathf.PI * 2f / splatsPerInterval;

        Vector2 center = new Vector2(0.5f, 0.5f);
        Vector2 offset = new Vector2(
            orbitRadius * Mathf.Cos(angle),
            orbitRadius * Mathf.Sin(angle)
        );

        Vector2 pos = center + offset;

        // Orbital force (tangential + slight inward)
        Vector2 tangent = new Vector2(-Mathf.Sin(angle), Mathf.Cos(angle));
        Vector2 inward = -offset.normalized;
        Vector2 force = (tangent * 30f + inward * 10f);

        // Color cycles through spectrum
        float hue = Mathf.Repeat(time * colorCycleSpeed + index * 0.25f, 1f);
        Color color = Color.HSVToRGB(hue, 0.85f, 1.0f) * idleSplatIntensity;

        return (pos, force, color);
    }

    void AddIdleSplat(Vector2 uv, Color col, Vector2 force)
    {
        int w = simResolution;
        int h = simResolution;
        if (kernelAddSplat < 0) return;
        fluidCompute.SetInts("_SimResolution", new int[] { w, h });
        fluidCompute.SetFloat("_TimeStep", timeStep);
        fluidCompute.SetFloat("_Radius", splatRadius);
        fluidCompute.SetFloats("_SplatColor", new float[] { col.r, col.g, col.b, 1.0f });
        fluidCompute.SetFloats("_SplatPos", new float[] { uv.x, uv.y });
        fluidCompute.SetFloats("_SplatForce", new float[] { force.x, force.y });
        fluidCompute.SetTexture(kernelAddSplat, "_Dye", dyeRT);
        fluidCompute.SetTexture(kernelAddSplat, "_DyeOut", dyeRT2);
        fluidCompute.SetTexture(kernelAddSplat, "_Velocity", velocityRT);
        fluidCompute.SetTexture(kernelAddSplat, "_VelocityOut", velocityRT2);
        DispatchForKernel(kernelAddSplat, w, h);
        Swap(ref dyeRT, ref dyeRT2);
        Swap(ref velocityRT, ref velocityRT2);
    }

    void DispatchForKernel(int kernel, int width, int height, int depth = 1)
    {
        if (kernel < 0) return;
        if (!kernelThreadSizes.TryGetValue(kernel, out var t))
        {
            int gx = Mathf.CeilToInt(width / 8.0f);
            int gy = Mathf.CeilToInt(height / 8.0f);
            try { fluidCompute.Dispatch(kernel, gx, gy, depth); }
            catch (System.Exception ex) { Debug.LogError($"FluidSim: Dispatch failed for kernel {kernel}: {ex.Message}"); enabled = false; }
            return;
        }
        int groupX = Mathf.CeilToInt(width / (float)t.x);
        int groupY = Mathf.CeilToInt(height / (float)t.y);
        try
        {
            fluidCompute.Dispatch(kernel, groupX, groupY, depth);
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"FluidSim: Dispatch failed for kernel {kernel} with exception: {ex.Message}\nDisabling simulation.");
            enabled = false;
        }
    }

    void Swap(ref RenderTexture a, ref RenderTexture b) { var t = a; a = b; b = t; }

    void HandleInputSplat()
    {
        if (Input.GetMouseButtonDown(0) || Input.touchCount > 0)
        {
            Vector2 uv;
            Vector2 force = Vector2.zero;
            Color col = Color.white;
            if (Input.touchCount > 0)
            {
                var t = Input.GetTouch(0);
                uv = cam.ScreenToViewportPoint(t.position);
                force = new Vector2(0, 0); // optionally calculate from delta position
            }
            else
            {
                uv = cam.ScreenToViewportPoint(Input.mousePosition);
                if (Input.GetMouseButton(0))
                {
                    var dx = Input.GetAxis("Mouse X");
                    var dy = Input.GetAxis("Mouse Y");
                    force = new Vector2(dx, dy) * 50f;
                }
            }
            col = Color.HSVToRGB(Mathf.Repeat(Time.time * 0.1f + uv.x, 1f), 0.7f, 1f);
            AddIdleSplat(uv, col, force);
        }
    }

    void OnDisable()
    {
        ReleaseRTs();
    }

    void OnDestroy()
    {
        ReleaseRTs();
    }
}
