using UnityEngine;
using UnityEngine.EventSystems;
using TMPro;
using LitMotion;
using LitMotion.Extensions;

namespace UGUIAnimationSamples
{
    public class Button1 : <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IPointerDownHandler, IPointerUpHandler
    {
        [Header("Components")]
        [SerializeField] RectTransform rectTransform;
        [SerializeField] TMP_Text text;

        [Header("Settings")]
        [SerializeField] float duration = 0.07f;
        [SerializeField] Ease ease = Ease.OutQuad;
        [SerializeField] Vector2 animationSizeDelta = new(14f, 7f);
        [SerializeField] int animationFontSizeDelta = 1;

        Vector2 initialSize;
        int initialFontSize;

        CompositeMotionHandle motionHandles = new();

        void Start()
        {
            initialSize = rectTransform.sizeDelta;
            initialFontSize = Mathf.RoundToInt(text.fontSize);
        }

        void OnDestroy()
        {
            motionHandles.Cancel();
        }

        public void OnPointerDown(PointerEventData eventData)
        {
            motionHandles.Cancel();

            LMotion.Create(rectTransform.sizeDelta, initialSize - animationSizeDelta, duration)
                .WithEase(ease)
                .BindToSizeDelta(rectTransform)
                .AddTo(motionHandles);

            LMotion.Create((int)Mathf.Round(text.fontSize), initialFontSize - animationFontSizeDelta, duration)
                .WithEase(ease)
                .BindWithState(text, (fs, t) => { if (t != null) t.fontSize = fs; })
                .AddTo(motionHandles);
        }

        public void OnPointerUp(PointerEventData eventData)
        {
            motionHandles.Cancel();

            LMotion.Create(rectTransform.sizeDelta, initialSize, duration)
                .WithEase(ease)
                .BindToSizeDelta(rectTransform)
                .AddTo(motionHandles);

            LMotion.Create((int)Mathf.Round(text.fontSize), initialFontSize, duration)
                .WithEase(ease)
                .BindWithState(text, (fs, t) => { if (t != null) t.fontSize = fs; })
                .AddTo(motionHandles);
        }
    }
}
