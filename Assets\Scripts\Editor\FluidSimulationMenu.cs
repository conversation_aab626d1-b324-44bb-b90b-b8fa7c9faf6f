using UnityEngine;
using UnityEditor;
using UnityEngine.UI;

/// <summary>
/// Editor menu items for easy fluid simulation setup
/// </summary>
public class FluidSimulationMenu
{
    [MenuItem("GameObject/UI/Fluid Simulation", false, 2001)]
    public static void CreateFluidSimulation()
    {
        // Find or create canvas
        Canvas canvas = Object.FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            GameObject canvasGO = new GameObject("Canvas");
            canvas = canvasGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasGO.AddComponent<CanvasScaler>();
            canvasGO.AddComponent<GraphicRaycaster>();
            
            Undo.RegisterCreatedObjectUndo(canvasGO, "Create Fluid Simulation Canvas");
        }
        
        // Create the fluid setup GameObject
        GameObject fluidSetupGO = new GameObject("FluidSimulationSetup");
        fluidSetupGO.transform.SetParent(canvas.transform, false);
        
        FluidSceneSetup setup = fluidSetupGO.AddComponent<FluidSceneSetup>();
        setup.setupOnStart = true;
        
        // Try to auto-assign resources
        setup.fluidCompute = AssetDatabase.LoadAssetAtPath<ComputeShader>("Assets/Shaders/Fluid.compute");
        
        Undo.RegisterCreatedObjectUndo(fluidSetupGO, "Create Fluid Simulation");
        Selection.activeGameObject = fluidSetupGO;
        
        Debug.Log("Fluid Simulation setup created! Play the scene to see it in action.");
    }
    
    [MenuItem("Tools/Fluid Simulation/Setup Current Scene")]
    public static void SetupCurrentScene()
    {
        FluidSceneSetup setup = Object.FindObjectOfType<FluidSceneSetup>();
        if (setup == null)
        {
            GameObject setupGO = new GameObject("FluidSceneSetup");
            setup = setupGO.AddComponent<FluidSceneSetup>();
            Undo.RegisterCreatedObjectUndo(setupGO, "Create Fluid Scene Setup");
        }
        
        setup.SetupFluidScene();
        Selection.activeGameObject = setup.gameObject;
    }
    
    [MenuItem("Tools/Fluid Simulation/Create Materials")]
    public static void CreateFluidMaterials()
    {
        // Create FluidBlit material
        Shader blitShader = Shader.Find("Hidden/FluidBlit");
        if (blitShader != null)
        {
            Material blitMat = new Material(blitShader);
            AssetDatabase.CreateAsset(blitMat, "Assets/Materials/FluidBlit.mat");
            Debug.Log("Created FluidBlit material at Assets/Materials/FluidBlit.mat");
        }
        
        // Create FluidUnlit material
        Shader unlitShader = Shader.Find("Custom/FluidWatercolorUnlit");
        if (unlitShader != null)
        {
            Material unlitMat = new Material(unlitShader);
            AssetDatabase.CreateAsset(unlitMat, "Assets/Materials/FluidUnlit.mat");
            Debug.Log("Created FluidUnlit material at Assets/Materials/FluidUnlit.mat");
        }
        
        AssetDatabase.Refresh();
    }
    
    [MenuItem("Tools/Fluid Simulation/Open Documentation")]
    public static void OpenDocumentation()
    {
        string readmePath = Application.dataPath + "/../FluidSimulation_README.md";
        if (System.IO.File.Exists(readmePath))
        {
            Application.OpenURL("file://" + readmePath);
        }
        else
        {
            Debug.LogWarning("Documentation file not found at: " + readmePath);
        }
    }
}