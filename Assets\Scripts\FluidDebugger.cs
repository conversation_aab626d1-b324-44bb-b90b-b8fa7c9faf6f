using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Debug script to help identify fluid simulation issues
/// </summary>
public class FluidDebugger : MonoBehaviour
{
    [Header("Debug Settings")]
    public bool enableDebugLogs = true;
    public bool showTextureInfo = true;
    public bool forceConstantSplats = true;
    
    [Header("References")]
    public RawImage targetRawImage;
    public FluidSim fluidSim;
    
    private float debugTimer = 0f;
    private int frameCount = 0;
    
    void Update()
    {
        debugTimer += Time.deltaTime;
        frameCount++;
        
        if (debugTimer > 1.0f) // Debug every second
        {
            debugTimer = 0f;
            DebugFluidState();
        }
        
        // Force constant splats to keep colors visible
        if (forceConstantSplats && fluidSim != null)
        {
            if (frameCount % 30 == 0) // Every 30 frames (about 0.5 seconds at 60fps)
            {
                AddDebugSplat();
            }
        }
    }
    
    void DebugFluidState()
    {
        if (!enableDebugLogs) return;
        
        Debug.Log("=== Fluid Debug Info ===");
        
        // Check RawImage
        if (targetRawImage != null)
        {
            Debug.Log($"RawImage texture: {(targetRawImage.texture != null ? targetRawImage.texture.name : "NULL")}");
            Debug.Log($"RawImage material: {(targetRawImage.material != null ? targetRawImage.material.name : "NULL")}");
            Debug.Log($"RawImage color: {targetRawImage.color}");
            
            if (showTextureInfo && targetRawImage.texture != null)
            {
                var tex = targetRawImage.texture;
                Debug.Log($"Texture size: {tex.width}x{tex.height}");
                Debug.Log($"Texture format: {tex.graphicsFormat}");
                
                if (tex is RenderTexture rt)
                {
                    Debug.Log($"RenderTexture created: {rt.IsCreated()}");
                    Debug.Log($"RenderTexture format: {rt.format}");
                }
            }
        }
        else
        {
            Debug.LogWarning("No RawImage assigned to debugger!");
        }
        
        // Check FluidSim
        if (fluidSim != null)
        {
            Debug.Log($"FluidSim enabled: {fluidSim.enabled}");
            Debug.Log($"FluidSim resolution: {fluidSim.simResolution}");
            Debug.Log($"FluidSim dye dissipation: {fluidSim.dyeDissipation}");
            Debug.Log($"FluidSim velocity dissipation: {fluidSim.velocityDissipation}");
            Debug.Log($"FluidSim compute shader: {(fluidSim.fluidCompute != null ? "Assigned" : "NULL")}");
        }
        else
        {
            Debug.LogWarning("No FluidSim assigned to debugger!");
        }
        
        Debug.Log("========================");
    }
    
    void AddDebugSplat()
    {
        if (fluidSim == null) return;
        
        // Add a bright splat at a random position
        Vector2 randomPos = new Vector2(Random.Range(0.2f, 0.8f), Random.Range(0.2f, 0.8f));
        Color brightColor = Color.HSVToRGB(Random.value, 1.0f, 1.0f) * 3.0f; // Very bright
        Vector2 strongForce = new Vector2(Random.Range(-30f, 30f), Random.Range(-30f, 30f));
        
        // Use reflection to call AddIdleSplat
        var method = typeof(FluidSim).GetMethod("AddIdleSplat", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (method != null)
        {
            method.Invoke(fluidSim, new object[] { randomPos, brightColor, strongForce });
            if (enableDebugLogs)
                Debug.Log($"Added debug splat at {randomPos} with color {brightColor}");
        }
    }
    
    [ContextMenu("Force Add Splat")]
    public void ForceAddSplat()
    {
        AddDebugSplat();
    }
    
    [ContextMenu("Check Texture Content")]
    public void CheckTextureContent()
    {
        if (targetRawImage?.texture is RenderTexture rt)
        {
            // Try to read a few pixels from the render texture
            RenderTexture.active = rt;
            Texture2D temp = new Texture2D(rt.width, rt.height, TextureFormat.RGBA32, false);
            temp.ReadPixels(new Rect(0, 0, rt.width, rt.height), 0, 0);
            temp.Apply();
            RenderTexture.active = null;
            
            // Check center pixel
            Color centerPixel = temp.GetPixel(rt.width / 2, rt.height / 2);
            Debug.Log($"Center pixel color: {centerPixel}");
            
            // Check if any pixels are non-black
            bool hasColor = false;
            for (int x = 0; x < temp.width && !hasColor; x += 10)
            {
                for (int y = 0; y < temp.height && !hasColor; y += 10)
                {
                    Color pixel = temp.GetPixel(x, y);
                    if (pixel.r > 0.01f || pixel.g > 0.01f || pixel.b > 0.01f)
                    {
                        hasColor = true;
                        Debug.Log($"Found color at ({x},{y}): {pixel}");
                    }
                }
            }
            
            if (!hasColor)
                Debug.LogWarning("Texture appears to be completely black!");
            
            DestroyImmediate(temp);
        }
        else
        {
            Debug.LogWarning("No RenderTexture to check!");
        }
    }
    
    void OnGUI()
    {
        if (!enableDebugLogs) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("Fluid Debug Info:");
        
        if (targetRawImage != null)
        {
            GUILayout.Label($"Texture: {(targetRawImage.texture != null ? "OK" : "NULL")}");
            GUILayout.Label($"Material: {(targetRawImage.material != null ? "OK" : "NULL")}");
        }
        
        if (fluidSim != null)
        {
            GUILayout.Label($"FluidSim: {(fluidSim.enabled ? "Enabled" : "Disabled")}");
            GUILayout.Label($"Dissipation: {fluidSim.dyeDissipation:F4}");
        }
        
        if (GUILayout.Button("Add Test Splat"))
        {
            AddDebugSplat();
        }
        
        if (GUILayout.Button("Check Texture"))
        {
            CheckTextureContent();
        }
        
        GUILayout.EndArea();
    }
}