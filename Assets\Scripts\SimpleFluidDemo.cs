using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Simplified fluid demo that directly displays the fluid simulation texture
/// without complex material setups
/// </summary>
public class SimpleFluidDemo : MonoBehaviour
{
    [Header("Required References")]
    public RawImage targetRawImage;
    public ComputeShader fluidCompute;
    
    [Header("Simulation Settings")]
    [Range(128, 512)]
    public int resolution = 256;
    [Range(0.0001f, 0.01f)]
    public float dyeDissipation = 0.00005f;  // Extremely low dissipation
    [Range(0.001f, 0.1f)]
    public float velocityDissipation = 0.001f;  // Very low velocity dissipation
    [Range(0.01f, 0.3f)]
    public float splatRadius = 0.2f;  // Larger splats
    
    [Header("Visual Enhancement")]
    [Range(0.5f, 10.0f)]
    public float colorIntensity = 5.0f;  // Much higher intensity
    [Range(0.1f, 2.0f)]
    public float idleSplatInterval = 0.2f;  // Very frequent splats
    public bool constantSplats = true;  // Add constant splats for debugging
    
    private FluidSim fluidSimComponent;
    private GameObject fluidCameraGO;
    private Material displayMaterial;
    
    void Start()
    {
        SetupFluidSimulation();
    }
    
    void SetupFluidSimulation()
    {
        if (targetRawImage == null)
        {
            Debug.LogError("SimpleFluidDemo: No RawImage assigned!");
            return;
        }
        
        if (fluidCompute == null)
        {
            Debug.LogError("SimpleFluidDemo: No ComputeShader assigned!");
            return;
        }
        
        // Create camera for fluid simulation
        fluidCameraGO = new GameObject("FluidSimCamera");
        Camera fluidCamera = fluidCameraGO.AddComponent<Camera>();
        fluidCamera.clearFlags = CameraClearFlags.SolidColor;
        fluidCamera.backgroundColor = Color.black;
        fluidCamera.orthographic = true;
        fluidCamera.orthographicSize = 1f;
        fluidCamera.depth = -100;
        fluidCamera.cullingMask = 0;
        fluidCamera.enabled = true;
        fluidCameraGO.transform.position = new Vector3(0, 0, -5);
        
        // Add FluidSim component
        fluidSimComponent = fluidCameraGO.AddComponent<FluidSim>();
        fluidSimComponent.simResolution = resolution;
        fluidSimComponent.fluidCompute = fluidCompute;
        fluidSimComponent.targetImage = targetRawImage;
        fluidSimComponent.dyeDissipation = dyeDissipation;
        fluidSimComponent.velocityDissipation = velocityDissipation;
        fluidSimComponent.splatRadius = splatRadius;
        fluidSimComponent.idleSplatInterval = idleSplatInterval;
        fluidSimComponent.idleSplatIntensity = colorIntensity;
        
        // Create a simple display material
        CreateDisplayMaterial();
        
        Debug.Log("SimpleFluidDemo: Setup complete!");
    }
    
    void CreateDisplayMaterial()
    {
        // Try to use the FluidBlit shader first
        Shader blitShader = Shader.Find("Hidden/FluidBlit");
        if (blitShader != null)
        {
            displayMaterial = new Material(blitShader);
            displayMaterial.SetFloat("_Intensity", colorIntensity);
            displayMaterial.SetFloat("_Brightness", 1.2f);
            displayMaterial.SetFloat("_Contrast", 1.1f);
            targetRawImage.material = displayMaterial;
            Debug.Log("Using FluidBlit shader for display");
        }
        else
        {
            // Fallback to UI/FluidDisplay shader
            Shader uiShader = Shader.Find("UI/FluidDisplay");
            if (uiShader != null)
            {
                displayMaterial = new Material(uiShader);
                displayMaterial.SetFloat("_ColorBoost", colorIntensity);
                displayMaterial.SetFloat("_Brightness", 1.5f);
                targetRawImage.material = displayMaterial;
                Debug.Log("Using UI/FluidDisplay shader for display");
            }
            else
            {
                // Final fallback - no material, just raw texture
                targetRawImage.material = null;
                Debug.Log("No shader found, using raw texture display");
            }
        }
    }
    
    void Update()
    {
        // Update simulation parameters in real-time
        if (fluidSimComponent != null)
        {
            fluidSimComponent.dyeDissipation = dyeDissipation;
            fluidSimComponent.velocityDissipation = velocityDissipation;
            fluidSimComponent.splatRadius = splatRadius;
            fluidSimComponent.idleSplatInterval = idleSplatInterval;
            fluidSimComponent.idleSplatIntensity = colorIntensity;
            
            // Update material properties if available
            if (displayMaterial != null)
            {
                if (displayMaterial.HasProperty("_Intensity"))
                    displayMaterial.SetFloat("_Intensity", colorIntensity);
                if (displayMaterial.HasProperty("_ColorBoost"))
                    displayMaterial.SetFloat("_ColorBoost", colorIntensity);
            }
        }
        
        // Handle mouse/touch input for interactive splats
        HandleInput();
    }
    
    void HandleInput()
    {
        if (fluidSimComponent == null || targetRawImage == null) return;
        
        if (Input.GetMouseButtonDown(0) || (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began))
        {
            Vector2 screenPos = Input.mousePosition;
            if (Input.touchCount > 0)
                screenPos = Input.GetTouch(0).position;
            
            // Convert screen position to UV coordinates
            RectTransform rectTransform = targetRawImage.GetComponent<RectTransform>();
            Camera uiCamera = null;
            Canvas canvas = targetRawImage.GetComponentInParent<Canvas>();
            if (canvas != null && canvas.renderMode != RenderMode.ScreenSpaceOverlay)
                uiCamera = canvas.worldCamera;
            
            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
                rectTransform, screenPos, uiCamera, out Vector2 localPoint))
            {
                Rect rect = rectTransform.rect;
                Vector2 uv = new Vector2(
                    (localPoint.x - rect.x) / rect.width,
                    (localPoint.y - rect.y) / rect.height
                );
                
                if (uv.x >= 0 && uv.x <= 1 && uv.y >= 0 && uv.y <= 1)
                {
                    AddInteractiveSplat(uv);
                }
            }
        }
    }
    
    void AddInteractiveSplat(Vector2 uv)
    {
        if (fluidSimComponent == null) return;
        
        Color splatColor = Color.HSVToRGB(Random.value, 0.8f, 1.0f) * colorIntensity;
        Vector2 force = new Vector2(Random.Range(-20f, 20f), Random.Range(-20f, 20f));
        
        // Use reflection to call AddIdleSplat
        var method = typeof(FluidSim).GetMethod("AddIdleSplat", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (method != null)
        {
            method.Invoke(fluidSimComponent, new object[] { uv, splatColor, force });
        }
    }
    
    void OnDestroy()
    {
        if (fluidCameraGO != null)
        {
            DestroyImmediate(fluidCameraGO);
        }
        
        if (displayMaterial != null)
        {
            DestroyImmediate(displayMaterial);
        }
    }
    
    [ContextMenu("Test Splat")]
    void TestSplat()
    {
        AddInteractiveSplat(new Vector2(0.5f, 0.5f));
    }
    
    [ContextMenu("Reset Simulation")]
    void ResetSimulation()
    {
        if (fluidCameraGO != null)
        {
            DestroyImmediate(fluidCameraGO);
        }
        SetupFluidSimulation();
    }
}