using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Direct test of fluid simulation without any complex materials or shaders
/// This will help us isolate if the issue is with the simulation or the display
/// </summary>
public class DirectFluidTest : MonoBehaviour
{
    [Header("Test Settings")]
    public RawImage testRawImage;
    public ComputeShader fluidCompute;
    
    [Header("Debug")]
    public bool showDebugInfo = true;
    public bool useNoMaterial = true;
    public bool forceMaxIntensity = true;
    
    private FluidSim fluidSim;
    private GameObject fluidCameraGO;
    private int testSplatCounter = 0;
    
    void Start()
    {
        SetupDirectTest();
    }
    
    void SetupDirectTest()
    {
        if (testRawImage == null || fluidCompute == null)
        {
            Debug.LogError("DirectFluidTest: Missing required references!");
            return;
        }
        
        // Create camera for fluid simulation
        fluidCameraGO = new GameObject("DirectTestFluidCamera");
        Camera cam = fluidCameraGO.AddComponent<Camera>();
        cam.clearFlags = CameraClearFlags.SolidColor;
        cam.backgroundColor = Color.black;
        cam.orthographic = true;
        cam.orthographicSize = 1f;
        cam.depth = -100;
        cam.cullingMask = 0;
        cam.enabled = true;
        fluidCameraGO.transform.position = new Vector3(0, 0, -5);
        
        // Add FluidSim with extreme settings
        fluidSim = fluidCameraGO.AddComponent<FluidSim>();
        fluidSim.simResolution = 256;
        fluidSim.fluidCompute = fluidCompute;
        fluidSim.targetImage = testRawImage;
        
        // EXTREME settings to force visibility
        fluidSim.dyeDissipation = 0.0f;  // NO dissipation at all
        fluidSim.velocityDissipation = 0.0f;  // NO velocity dissipation
        fluidSim.splatRadius = 0.3f;  // Very large splats
        fluidSim.idleSplatInterval = 0.1f;  // Very frequent
        fluidSim.idleSplatIntensity = 10.0f;  // Maximum intensity
        fluidSim.enableIdleAnimation = true;
        
        // Remove any material to test raw texture display
        if (useNoMaterial)
        {
            testRawImage.material = null;
            Debug.Log("DirectFluidTest: Using no material - raw texture display");
        }
        
        // Force white color on RawImage
        testRawImage.color = Color.white;
        
        Debug.Log("DirectFluidTest: Setup complete with EXTREME settings");
        Debug.Log($"Dissipation: dye={fluidSim.dyeDissipation}, vel={fluidSim.velocityDissipation}");
        Debug.Log($"Splat: radius={fluidSim.splatRadius}, interval={fluidSim.idleSplatInterval}, intensity={fluidSim.idleSplatIntensity}");
    }
    
    void Update()
    {
        // Force add test splats every few frames
        if (Time.frameCount % 60 == 0) // Every second at 60fps
        {
            AddTestSplat();
        }
        
        // Add splats on mouse click anywhere
        if (Input.GetMouseButtonDown(0))
        {
            AddTestSplat();
        }
        
        // Debug info
        if (showDebugInfo && Time.frameCount % 60 == 0)
        {
            DebugTextureState();
        }
    }
    
    void AddTestSplat()
    {
        if (fluidSim == null) return;
        
        testSplatCounter++;
        
        // Add a very bright, large splat
        Vector2 pos = new Vector2(
            0.3f + 0.4f * Mathf.Sin(Time.time * 0.5f),
            0.3f + 0.4f * Mathf.Cos(Time.time * 0.3f)
        );
        
        // Use maximum intensity colors
        Color color = Color.HSVToRGB((testSplatCounter * 0.1f) % 1f, 1.0f, 1.0f);
        if (forceMaxIntensity)
        {
            color *= 20.0f; // Extreme intensity
        }
        
        Vector2 force = new Vector2(
            Mathf.Sin(Time.time * 2f) * 50f,
            Mathf.Cos(Time.time * 1.5f) * 50f
        );
        
        // Use reflection to call AddIdleSplat
        var method = typeof(FluidSim).GetMethod("AddIdleSplat", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (method != null)
        {
            method.Invoke(fluidSim, new object[] { pos, color, force });
            Debug.Log($"DirectFluidTest: Added test splat #{testSplatCounter} at {pos} with color {color}");
        }
    }
    
    void DebugTextureState()
    {
        if (testRawImage?.texture == null)
        {
            Debug.LogWarning("DirectFluidTest: RawImage has no texture!");
            return;
        }
        
        var tex = testRawImage.texture;
        Debug.Log($"DirectFluidTest: Texture = {tex.name}, Size = {tex.width}x{tex.height}");
        
        if (tex is RenderTexture rt)
        {
            Debug.Log($"DirectFluidTest: RenderTexture created = {rt.IsCreated()}, format = {rt.format}");
            
            // Try to sample the texture
            RenderTexture.active = rt;
            Texture2D temp = new Texture2D(rt.width, rt.height, TextureFormat.RGBA32, false);
            temp.ReadPixels(new Rect(0, 0, rt.width, rt.height), 0, 0);
            temp.Apply();
            RenderTexture.active = null;
            
            // Check a few pixels
            Color center = temp.GetPixel(rt.width / 2, rt.height / 2);
            Color corner = temp.GetPixel(rt.width / 4, rt.height / 4);
            
            Debug.Log($"DirectFluidTest: Center pixel = {center}, Corner pixel = {corner}");
            
            // Check if we have any non-black pixels
            bool hasColor = false;
            for (int x = 0; x < temp.width && !hasColor; x += 20)
            {
                for (int y = 0; y < temp.height && !hasColor; y += 20)
                {
                    Color pixel = temp.GetPixel(x, y);
                    if (pixel.r > 0.01f || pixel.g > 0.01f || pixel.b > 0.01f)
                    {
                        hasColor = true;
                        Debug.Log($"DirectFluidTest: Found color at ({x},{y}) = {pixel}");
                    }
                }
            }
            
            if (!hasColor)
            {
                Debug.LogError("DirectFluidTest: Texture is completely black! Simulation may not be working.");
            }
            
            DestroyImmediate(temp);
        }
    }
    
    void OnGUI()
    {
        if (!showDebugInfo) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 400, 300));
        GUILayout.Label("=== DIRECT FLUID TEST ===");
        
        if (fluidSim != null)
        {
            GUILayout.Label($"FluidSim Enabled: {fluidSim.enabled}");
            GUILayout.Label($"Compute Shader: {(fluidSim.fluidCompute != null ? "OK" : "NULL")}");
            GUILayout.Label($"Dye Dissipation: {fluidSim.dyeDissipation}");
            GUILayout.Label($"Splat Intensity: {fluidSim.idleSplatIntensity}");
        }
        
        if (testRawImage != null)
        {
            GUILayout.Label($"RawImage Texture: {(testRawImage.texture != null ? "OK" : "NULL")}");
            GUILayout.Label($"RawImage Material: {(testRawImage.material != null ? testRawImage.material.name : "NULL")}");
            GUILayout.Label($"RawImage Color: {testRawImage.color}");
        }
        
        GUILayout.Label($"Test Splats Added: {testSplatCounter}");
        
        if (GUILayout.Button("Add Test Splat NOW"))
        {
            AddTestSplat();
        }
        
        if (GUILayout.Button("Check Texture Content"))
        {
            DebugTextureState();
        }
        
        if (GUILayout.Button("Toggle Material"))
        {
            if (testRawImage.material == null)
            {
                // Try to create a simple material
                Shader shader = Shader.Find("Hidden/FluidBlit");
                if (shader != null)
                {
                    testRawImage.material = new Material(shader);
                    testRawImage.material.SetFloat("_Intensity", 10.0f);
                }
            }
            else
            {
                testRawImage.material = null;
            }
        }
        
        GUILayout.EndArea();
    }
    
    void OnDestroy()
    {
        if (fluidCameraGO != null)
        {
            DestroyImmediate(fluidCameraGO);
        }
    }
    
    [ContextMenu("Force Add Multiple Splats")]
    void ForceAddMultipleSplats()
    {
        for (int i = 0; i < 10; i++)
        {
            AddTestSplat();
        }
    }
}