using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Test if the compute shader is working at all
/// </summary>
public class ComputeShaderTest : MonoBehaviour
{
    [Header("Test Settings")]
    public RawImage testRawImage;
    public ComputeShader fluidCompute;
    
    [Header("Debug")]
    public bool showDebugInfo = true;
    
    private RenderTexture testTexture;
    private int testKernel = -1;
    
    void Start()
    {
        TestComputeShader();
    }
    
    void TestComputeShader()
    {
        if (fluidCompute == null)
        {
            Debug.LogError("ComputeShaderTest: No compute shader assigned!");
            return;
        }
        
        if (!SystemInfo.supportsComputeShaders)
        {
            Debug.LogError("ComputeShaderTest: Compute shaders not supported on this platform!");
            return;
        }
        
        Debug.Log("ComputeShaderTest: Testing compute shader functionality...");
        
        // Create a simple test texture
        testTexture = new RenderTexture(256, 256, 0, RenderTextureFormat.ARGBFloat);
        testTexture.enableRandomWrite = true;
        testTexture.Create();
        
        // Clear to black
        Graphics.Blit(Texture2D.blackTexture, testTexture);
        
        // Try to find the AddSplat kernel
        try
        {
            testKernel = fluidCompute.FindKernel("AddSplat");
            Debug.Log($"ComputeShaderTest: Found AddSplat kernel with ID: {testKernel}");
        }
        catch (System.Exception e)
        {
            Debug.LogError($"ComputeShaderTest: Failed to find AddSplat kernel: {e.Message}");
            return;
        }
        
        // Test adding a simple splat
        TestAddSplat();
        
        // Assign to Raw Image
        if (testRawImage != null)
        {
            testRawImage.texture = testTexture;
            testRawImage.material = null; // No material
            testRawImage.color = Color.white;
        }
    }
    
    void TestAddSplat()
    {
        if (testKernel < 0 || testTexture == null) return;
        
        try
        {
            // Set up compute shader parameters for a simple test
            fluidCompute.SetInts("_SimResolution", new int[] { 256, 256 });
            fluidCompute.SetFloat("_TimeStep", 0.016f);
            fluidCompute.SetFloat("_Radius", 0.2f);
            fluidCompute.SetFloats("_SplatColor", new float[] { 1.0f, 0.0f, 0.0f, 1.0f }); // Red
            fluidCompute.SetFloats("_SplatPos", new float[] { 0.5f, 0.5f }); // Center
            fluidCompute.SetFloats("_SplatForce", new float[] { 0.0f, 0.0f }); // No force
            
            // Create dummy velocity textures
            RenderTexture dummyVel = new RenderTexture(256, 256, 0, RenderTextureFormat.ARGBFloat);
            dummyVel.enableRandomWrite = true;
            dummyVel.Create();
            Graphics.Blit(Texture2D.blackTexture, dummyVel);
            
            RenderTexture dummyVelOut = new RenderTexture(256, 256, 0, RenderTextureFormat.ARGBFloat);
            dummyVelOut.enableRandomWrite = true;
            dummyVelOut.Create();
            
            // Set textures
            fluidCompute.SetTexture(testKernel, "_Dye", testTexture);
            fluidCompute.SetTexture(testKernel, "_DyeOut", testTexture); // Write to same texture for test
            fluidCompute.SetTexture(testKernel, "_Velocity", dummyVel);
            fluidCompute.SetTexture(testKernel, "_VelocityOut", dummyVelOut);
            
            // Dispatch
            int groupX = Mathf.CeilToInt(256 / 8.0f);
            int groupY = Mathf.CeilToInt(256 / 8.0f);
            fluidCompute.Dispatch(testKernel, groupX, groupY, 1);
            
            Debug.Log("ComputeShaderTest: Successfully dispatched AddSplat kernel");
            
            // Clean up dummy textures
            dummyVel.Release();
            dummyVelOut.Release();
            
            // Check if the splat was added
            CheckTextureContent();
        }
        catch (System.Exception e)
        {
            Debug.LogError($"ComputeShaderTest: Failed to dispatch compute shader: {e.Message}");
        }
    }
    
    void CheckTextureContent()
    {
        if (testTexture == null) return;
        
        RenderTexture.active = testTexture;
        Texture2D temp = new Texture2D(testTexture.width, testTexture.height, TextureFormat.RGBA32, false);
        temp.ReadPixels(new Rect(0, 0, testTexture.width, testTexture.height), 0, 0);
        temp.Apply();
        RenderTexture.active = null;
        
        // Check center pixel (where we added the splat)
        Color centerPixel = temp.GetPixel(testTexture.width / 2, testTexture.height / 2);
        Debug.Log($"ComputeShaderTest: Center pixel after splat: {centerPixel}");
        
        // Check if any pixels are non-black
        bool hasColor = false;
        int colorPixels = 0;
        for (int x = 0; x < temp.width; x += 4)
        {
            for (int y = 0; y < temp.height; y += 4)
            {
                Color pixel = temp.GetPixel(x, y);
                if (pixel.r > 0.01f || pixel.g > 0.01f || pixel.b > 0.01f)
                {
                    hasColor = true;
                    colorPixels++;
                }
            }
        }
        
        Debug.Log($"ComputeShaderTest: Found {colorPixels} colored pixels out of {(temp.width/4) * (temp.height/4)} sampled");
        
        if (hasColor)
        {
            Debug.Log("ComputeShaderTest: SUCCESS - Compute shader is working!");
        }
        else
        {
            Debug.LogError("ComputeShaderTest: FAILED - Compute shader did not add any color to texture");
        }
        
        DestroyImmediate(temp);
    }
    
    void Update()
    {
        // Add more splats over time for testing
        if (Time.frameCount % 60 == 0) // Every second
        {
            TestAddSplat();
        }
    }
    
    void OnGUI()
    {
        if (!showDebugInfo) return;
        
        GUILayout.BeginArea(new Rect(10, 10, 300, 200));
        GUILayout.Label("=== COMPUTE SHADER TEST ===");
        GUILayout.Label($"Compute Support: {SystemInfo.supportsComputeShaders}");
        GUILayout.Label($"Compute Shader: {(fluidCompute != null ? "Assigned" : "NULL")}");
        GUILayout.Label($"Test Kernel: {(testKernel >= 0 ? "Found" : "Not Found")}");
        GUILayout.Label($"Test Texture: {(testTexture != null && testTexture.IsCreated() ? "Created" : "Failed")}");
        
        if (GUILayout.Button("Test Add Splat"))
        {
            TestAddSplat();
        }
        
        if (GUILayout.Button("Check Texture"))
        {
            CheckTextureContent();
        }
        
        GUILayout.EndArea();
    }
    
    void OnDestroy()
    {
        if (testTexture != null)
        {
            testTexture.Release();
        }
    }
}