using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Demo script to showcase fluid simulation on UI Raw Image
/// Automatically sets up the fluid simulation for UI display
/// </summary>
public class FluidUIDemo : MonoBehaviour
{
    [Header("UI Setup")]
    public RawImage fluidRawImage;
    public Canvas parentCanvas;
    
    [Header("Fluid Settings")]
    public ComputeShader fluidCompute;
    public Material fluidBlitMaterial;
    public Material fluidUnlitMaterial;
    
    [Header("Simulation Parameters")]
    [Range(128, 512)]
    public int resolution = 256;
    [Range(0.001f, 0.1f)]
    public float velocityDissipation = 0.02f;
    [Range(0.001f, 0.1f)]
    public float dyeDissipation = 0.005f;
    [Range(0.01f, 0.2f)]
    public float splatRadius = 0.08f;
    [Range(10, 50)]
    public int jacobiIterations = 25;
    
    [Header("Idle Animation")]
    public bool enableIdleAnimation = true;
    [Range(0.1f, 3.0f)]
    public float idleSplatInterval = 1.2f;
    [Range(0.5f, 3.0f)]
    public float colorCycleSpeed = 1.0f;
    [Range(5f, 30f)]
    public float forceStrength = 15f;
    
    private FluidSim fluidSimComponent;
    private Camera fluidCamera;
    private GameObject fluidCameraGO;
    
    void Start()
    {
        SetupFluidSimulation();
    }
    
    void SetupFluidSimulation()
    {
        // Create a camera for the fluid simulation
        fluidCameraGO = new GameObject("FluidCamera");
        fluidCamera = fluidCameraGO.AddComponent<Camera>();
        
        // Configure camera for offscreen rendering
        fluidCamera.clearFlags = CameraClearFlags.SolidColor;
        fluidCamera.backgroundColor = Color.black;
        fluidCamera.orthographic = true;
        fluidCamera.orthographicSize = 1f;
        fluidCamera.nearClipPlane = 0.1f;
        fluidCamera.farClipPlane = 10f;
        fluidCamera.depth = -100; // Render before main camera
        fluidCamera.cullingMask = 0; // Don't render any objects
        fluidCamera.enabled = true;
        
        // Position camera
        fluidCameraGO.transform.position = new Vector3(0, 0, -5);
        
        // Add and configure FluidSim component
        fluidSimComponent = fluidCameraGO.AddComponent<FluidSim>();
        fluidSimComponent.simResolution = resolution;
        fluidSimComponent.fluidCompute = fluidCompute;
        fluidSimComponent.targetImage = fluidRawImage;
        fluidSimComponent.blitMaterial = fluidBlitMaterial;
        
        // Configure simulation parameters
        fluidSimComponent.velocityDissipation = velocityDissipation;
        fluidSimComponent.dyeDissipation = dyeDissipation;
        fluidSimComponent.splatRadius = splatRadius;
        fluidSimComponent.jacobiIterations = jacobiIterations;
        
        // Setup Raw Image
        if (fluidRawImage != null)
        {
            // Apply the fluid UI material for proper display
            if (fluidUnlitMaterial != null)
            {
                fluidRawImage.material = fluidUnlitMaterial;
            }
            else
            {
                // Create a simple material for fluid display if none provided
                Shader fluidUIShader = Shader.Find("UI/FluidDisplay");
                if (fluidUIShader != null)
                {
                    fluidRawImage.material = new Material(fluidUIShader);
                }
                else
                {
                    // Fallback to default UI shader
                    fluidRawImage.material = new Material(Shader.Find("UI/Default"));
                }
            }
            
            // Ensure proper aspect ratio
            var rectTransform = fluidRawImage.GetComponent<RectTransform>();
            if (rectTransform != null)
            {
                rectTransform.sizeDelta = new Vector2(resolution, resolution);
            }
        }
        
        Debug.Log("Fluid UI Demo: Simulation setup complete!");
    }
    
    void Update()
    {
        // Update simulation parameters in real-time
        if (fluidSimComponent != null)
        {
            fluidSimComponent.velocityDissipation = velocityDissipation;
            fluidSimComponent.dyeDissipation = dyeDissipation;
            fluidSimComponent.splatRadius = splatRadius;
            fluidSimComponent.jacobiIterations = jacobiIterations;
        }
        
        // Handle manual input for additional splats
        HandleUserInput();
    }
    
    void HandleUserInput()
    {
        if (fluidSimComponent == null || fluidRawImage == null) return;
        
        // Check for mouse/touch input over the Raw Image
        if (Input.GetMouseButtonDown(0) || (Input.touchCount > 0 && Input.GetTouch(0).phase == TouchPhase.Began))
        {
            Vector2 screenPos = Input.mousePosition;
            if (Input.touchCount > 0)
                screenPos = Input.GetTouch(0).position;
            
            // Convert screen position to Raw Image UV coordinates
            RectTransform rectTransform = fluidRawImage.GetComponent<RectTransform>();
            if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
                rectTransform, screenPos, parentCanvas.worldCamera, out Vector2 localPoint))
            {
                // Convert local point to UV (0-1 range)
                Rect rect = rectTransform.rect;
                Vector2 uv = new Vector2(
                    (localPoint.x - rect.x) / rect.width,
                    (localPoint.y - rect.y) / rect.height
                );
                
                // Ensure UV is in valid range
                if (uv.x >= 0 && uv.x <= 1 && uv.y >= 0 && uv.y <= 1)
                {
                    // Add a splat at the touch/click position
                    AddInteractiveSplat(uv);
                }
            }
        }
    }
    
    void AddInteractiveSplat(Vector2 uv)
    {
        if (fluidSimComponent == null) return;
        
        // Create a colorful splat with some random force
        Color splatColor = Color.HSVToRGB(
            Mathf.Repeat(Time.time * colorCycleSpeed + uv.x + uv.y, 1f), 
            0.8f, 
            1.0f
        );
        
        Vector2 force = new Vector2(
            (Random.value - 0.5f) * forceStrength * 2f,
            (Random.value - 0.5f) * forceStrength * 2f
        );
        
        // Use reflection to call the private AddIdleSplat method
        var method = typeof(FluidSim).GetMethod("AddIdleSplat", 
            System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
        if (method != null)
        {
            method.Invoke(fluidSimComponent, new object[] { uv, splatColor, force });
        }
    }
    
    void OnValidate()
    {
        // Update parameters when changed in inspector
        if (Application.isPlaying && fluidSimComponent != null)
        {
            fluidSimComponent.simResolution = resolution;
            fluidSimComponent.velocityDissipation = velocityDissipation;
            fluidSimComponent.dyeDissipation = dyeDissipation;
            fluidSimComponent.splatRadius = splatRadius;
            fluidSimComponent.jacobiIterations = jacobiIterations;
        }
    }
    
    void OnDestroy()
    {
        // Clean up the fluid camera
        if (fluidCameraGO != null)
        {
            DestroyImmediate(fluidCameraGO);
        }
    }
}