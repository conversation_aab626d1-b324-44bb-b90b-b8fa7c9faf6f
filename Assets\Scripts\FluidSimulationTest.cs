using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Simple test script to verify fluid simulation is working
/// Add this to any GameObject to run basic tests
/// </summary>
public class FluidSimulationTest : MonoBehaviour
{
    [Header("Test Settings")]
    public bool runTestOnStart = true;
    public bool logDetailedInfo = true;
    
    void Start()
    {
        if (runTestOnStart)
        {
            RunFluidSimulationTest();
        }
    }
    
    [ContextMenu("Run Fluid Simulation Test")]
    public void RunFluidSimulationTest()
    {
        Debug.Log("=== Fluid Simulation Test Started ===");
        
        // Test 1: Check compute shader support
        bool computeSupported = SystemInfo.supportsComputeShaders;
        Debug.Log($"Compute Shaders Supported: {computeSupported}");
        if (!computeSupported)
        {
            Debug.LogError("Compute shaders are not supported on this platform! Fluid simulation will not work.");
            return;
        }
        
        // Test 2: Check for required assets
        ComputeShader fluidCompute = Resources.Load<ComputeShader>("Fluid");
#if UNITY_EDITOR
        if (fluidCompute == null)
        {
            // Try alternative path in editor
            fluidCompute = UnityEditor.AssetDatabase.LoadAssetAtPath<ComputeShader>("Assets/Shaders/Fluid.compute");
        }
#endif
        Debug.Log($"Fluid Compute Shader Found: {fluidCompute != null}");
        
        // Test 3: Check shaders
        Shader blitShader = Shader.Find("Hidden/FluidBlit");
        Shader unlitShader = Shader.Find("Custom/FluidWatercolorUnlit");
        Debug.Log($"FluidBlit Shader Found: {blitShader != null}");
        Debug.Log($"FluidUnlit Shader Found: {unlitShader != null}");
        
        // Test 4: Check for existing fluid simulation components
        FluidSim[] fluidSims = FindObjectsOfType<FluidSim>();
        FluidUIDemo[] fluidDemos = FindObjectsOfType<FluidUIDemo>();
        FluidSceneSetup[] sceneSetups = FindObjectsOfType<FluidSceneSetup>();
        
        Debug.Log($"FluidSim components in scene: {fluidSims.Length}");
        Debug.Log($"FluidUIDemo components in scene: {fluidDemos.Length}");
        Debug.Log($"FluidSceneSetup components in scene: {sceneSetups.Length}");
        
        // Test 5: Check UI setup
        Canvas[] canvases = FindObjectsOfType<Canvas>();
        RawImage[] rawImages = FindObjectsOfType<RawImage>();
        Debug.Log($"Canvas components in scene: {canvases.Length}");
        Debug.Log($"RawImage components in scene: {rawImages.Length}");
        
        // Test 6: Performance info
        if (logDetailedInfo)
        {
            Debug.Log($"Graphics Device: {SystemInfo.graphicsDeviceName}");
            Debug.Log($"Graphics API: {SystemInfo.graphicsDeviceType}");
            Debug.Log($"Max Texture Size: {SystemInfo.maxTextureSize}");
            Debug.Log($"Max Compute Buffer Inputs: {SystemInfo.maxComputeBufferInputsVertex}");
        }
        
        // Test 7: Try to create a simple test setup
        if (fluidSims.Length == 0 && sceneSetups.Length == 0)
        {
            Debug.Log("No fluid simulation found. Creating test setup...");
            CreateTestSetup();
        }
        
        Debug.Log("=== Fluid Simulation Test Completed ===");
    }
    
    void CreateTestSetup()
    {
        GameObject testGO = new GameObject("FluidSimulationTestSetup");
        FluidSceneSetup setup = testGO.AddComponent<FluidSceneSetup>();
        
        // Call the setup method directly instead of relying on setupOnStart
        setup.SetupFluidScene();
        
        Debug.Log("Test setup created and fluid scene configured!");
    }
    
    [ContextMenu("Force Setup Fluid Scene")]
    public void ForceSetupFluidScene()
    {
        FluidSceneSetup setup = FindObjectOfType<FluidSceneSetup>();
        if (setup == null)
        {
            GameObject setupGO = new GameObject("FluidSceneSetup");
            setup = setupGO.AddComponent<FluidSceneSetup>();
        }
        
        setup.SetupFluidScene();
        Debug.Log("Fluid scene setup forced!");
    }
    
    [ContextMenu("Test Splat Generation")]
    public void TestSplatGeneration()
    {
        FluidSim fluidSim = FindObjectOfType<FluidSim>();
        if (fluidSim != null)
        {
            // Use reflection to call AddIdleSplat
            var method = typeof(FluidSim).GetMethod("AddIdleSplat", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            if (method != null)
            {
                Vector2 randomPos = new Vector2(Random.value, Random.value);
                Color randomColor = Color.HSVToRGB(Random.value, 0.8f, 1.0f);
                Vector2 randomForce = new Vector2(Random.Range(-20f, 20f), Random.Range(-20f, 20f));
                
                method.Invoke(fluidSim, new object[] { randomPos, randomColor, randomForce });
                Debug.Log($"Test splat added at {randomPos} with color {randomColor}");
            }
            else
            {
                Debug.LogWarning("Could not find AddIdleSplat method");
            }
        }
        else
        {
            Debug.LogWarning("No FluidSim component found in scene");
        }
    }
}