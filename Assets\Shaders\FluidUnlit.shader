Shader "Custom/FluidWatercolorUnlit"
{
    Properties
    {
        _MainTex ("Base (unused)", 2D) = "white" {}
        _Scale ("Noise Scale", Float) = 1.6
        _Speed ("Flow Speed", Float) = 0.45
        _FlowStrength ("Flow Strength", Float) = 0.9
        _Turbulence ("Turbulence", Float) = 1.1
        _Contrast ("Contrast", Float) = 1.1
        _ColorA ("Color A", Color) = (0.2, 1, 0.6, 1)
        _ColorB ("Color B", Color) = (1.0, 0.95, 0.2, 1)
        _ColorC ("Color C", Color) = (0.0, 0.9, 1.0, 1)
        _Glow ("Glow Intensity", Float) = 1.2
        _Vignette ("Vignette", Float) = 0.6
        _Diffusion ("Diffusion (blur)", Range(0,2)) = 0.9
        _SampleRadius ("Sample Radius", Range(0,2)) = 0.9
        _Iterations ("Diffusion Iterations", Int) = 2
        _ShapeScale ("Shape Scale", Float) = 0.6
        _ShapeWeight ("Shape Weight", Range(0,1)) = 0.75
        _ShapeSmoothness ("Shape Smoothness", Range(0,0.5)) = 0.15
        _ShapeContrast ("Shape Contrast", Float) = 1.2
    }

    SubShader
    {
        Tags { "RenderType"="Opaque" "Queue"="Geometry" }
        LOD 100

        Pass
        {
            ZWrite Off
            Cull Off
            Fog { Mode Off }

            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            sampler2D _MainTex;
            float _Scale;
            float _Speed;
            float _FlowStrength;
            float _Turbulence;
            float _Contrast;
            float4 _ColorA;
            float4 _ColorB;
            float4 _ColorC;
            float _Glow;
            float _Vignette;
            float _Diffusion;
            float _SampleRadius;
            int _Iterations;
            float _ShapeScale;
            float _ShapeWeight;
            float _ShapeSmoothness;
            float _ShapeContrast;

            struct appdata
            {
                float4 vertex : POSITION;
                float2 uv : TEXCOORD0;
            };

            struct v2f
            {
                float2 uv : TEXCOORD0;
                float4 vertex : SV_POSITION;
            };

            v2f vert(appdata v)
            {
                v2f o;
                o.vertex = UnityObjectToClipPos(v.vertex);
                o.uv = v.uv;
                return o;
            }

            static float hash21(float2 p)
            {
                p = frac(p * float2(123.34, 345.45));
                p += dot(p, p + 34.345);
                return frac(p.x * p.y);
            }

            float noise(float2 p)
            {
                float2 i = floor(p);
                float2 f = frac(p);
                float a = hash21(i);
                float b = hash21(i + float2(1.0, 0.0));
                float c = hash21(i + float2(0.0, 1.0));
                float d = hash21(i + float2(1.0, 1.0));
                float2 u = f * f * (3.0 - 2.0 * f);
                return lerp(lerp(a, b, u.x), lerp(c, d, u.x), u.y);
            }

            float fbm(float2 p)
            {
                float v = 0.0;
                float a = 0.5;
                float2 shift = float2(100.0, 100.0);
                for (int i = 0; i < 5; ++i)
                {
                    v += a * noise(p);
                    p = p * 2.0 + shift;
                    a *= 0.5;
                }
                return v;
            }

            // lower-frequency fbm for large shapes (fewer octaves)
            float lowfbm(float2 p)
            {
                float v = 0.0;
                float a = 0.6;
                float2 shift = float2(47.0, 91.0);
                for (int i = 0; i < 3; ++i)
                {
                    v += a * noise(p);
                    p = p * 2.0 + shift;
                    a *= 0.5;
                }
                return v;
            }

            float2 curlNoise(float2 p)
            {
                float e = 0.001;
                float n1 = fbm(p + float2(e, 0.0));
                float n2 = fbm(p - float2(e, 0.0));
                float n3 = fbm(p + float2(0.0, e));
                float n4 = fbm(p - float2(0.0, e));
                float dx = (n1 - n2) / (2.0 * e);
                float dy = (n3 - n4) / (2.0 * e);
                return float2(dy, -dx);
            }

            // simple gaussian-like weight for blur taps
            float gaussWeight(float x)
            {
                return exp(-x * x * 0.5);
            }

            // subtractive mixing: convert RGB to CMY, mix, convert back
            float3 subtractiveMix(float3 a, float3 b)
            {
                float3 ca = 1.0 - a;
                float3 cb = 1.0 - b;
                float3 cm = saturate((ca + cb) * 0.5); // mix cyan-magenta-yellow
                return 1.0 - cm;
            }

            // multi-tap diffusion/blur of density field (cheap)
            float sampleDiffuse(float2 uv, float2 offset, float scale)
            {
                // 5-tap cross
                float s = 0.0;
                s += fbm((uv + offset) * scale) * 0.4;
                s += fbm((uv + offset + float2(0.01, 0.0)) * scale) * 0.15;
                s += fbm((uv + offset - float2(0.01, 0.0)) * scale) * 0.15;
                s += fbm((uv + offset + float2(0.0, 0.01)) * scale) * 0.15;
                s += fbm((uv + offset - float2(0.0, 0.01)) * scale) * 0.15;
                return s;
            }

            fixed4 frag(v2f i) : SV_Target
            {
                // normalized uv centered
                float2 uv = i.uv - 0.5;
                uv.x *= _ScreenParams.x / _ScreenParams.y;

                // base coordinate scaled
                float2 p = uv * _Scale;

                float t = _Time.y * _Speed;

                // low-frequency shape field
                float baseShape = lowfbm(uv * _ShapeScale + t * 0.02);
                baseShape = pow(saturate(baseShape), _ShapeContrast);
                float shapeMask = smoothstep(0.35 - _ShapeSmoothness, 0.7 + _ShapeSmoothness, baseShape);

                // advect positions for three separate color densities
                float2 qA = p;
                float2 qB = p + float2(10.0, -5.0);
                float2 qC = p + float2(-7.0, 8.0);

                // stronger multi-step advection
                for (int s = 0; s < 4; ++s)
                {
                    float2 ca = curlNoise(qA + t * (0.2 + s * 0.25));
                    float2 cb = curlNoise(qB + t * (0.15 + s * 0.22));
                    float2 cc = curlNoise(qC + t * (0.1 + s * 0.2));
                    qA += ca * (_FlowStrength * (0.6 + 0.6 * s));
                    qB += cb * (_FlowStrength * (0.6 + 0.5 * s));
                    qC += cc * (_FlowStrength * (0.6 + 0.4 * s));

                    qA += (fbm(qA * (1.0 + s * 0.6) + t * (0.2 + s * 0.15)) - 0.5) * (_Turbulence * 0.08);
                    qB += (fbm(qB * (1.0 + s * 0.5) + t * (0.25 + s * 0.12)) - 0.5) * (_Turbulence * 0.08);
                    qC += (fbm(qC * (1.0 + s * 0.45) + t * (0.18 + s * 0.1)) - 0.5) * (_Turbulence * 0.08);
                }

                // base densities
                float dA = fbm(qA + t * 0.4);
                float dB = fbm(qB - t * 0.3);
                float dC = fbm(qC + t * 0.2);

                // bring to 0..1
                dA = smoothstep(0.12, 0.85, dA);
                dB = smoothstep(0.10, 0.88, dB);
                dC = smoothstep(0.14, 0.83, dC);

                // apply diffusion via cheap multi-tap blur iterations
                float2 screenUV = i.uv;
                float radius = _SampleRadius * 0.02 * (_Scale);

                float accumA = dA;
                float accumB = dB;
                float accumC = dC;

                for (int it = 0; it < max(1, _Iterations); ++it)
                {
                    float a0 = sampleDiffuse(screenUV, float2(0,0), _Scale * (1.0 + it * 0.3));
                    float a1 = sampleDiffuse(screenUV, float2(radius,0), _Scale * (1.0 + it * 0.3));
                    float a2 = sampleDiffuse(screenUV, float2(-radius,0), _Scale * (1.0 + it * 0.3));
                    float a3 = sampleDiffuse(screenUV, float2(0,radius), _Scale * (1.0 + it * 0.3));
                    float a4 = sampleDiffuse(screenUV, float2(0,-radius), _Scale * (1.0 + it * 0.3));

                    float sA = a0 * 0.4 + (a1 + a2 + a3 + a4) * 0.15;

                    // sample other channels with slight offsets for color bleeding
                    float b0 = sampleDiffuse(screenUV + float2(0.015, -0.01), float2(0,0), _Scale * (1.1 + it * 0.25));
                    float c0 = sampleDiffuse(screenUV + float2(-0.012, 0.02), float2(0,0), _Scale * (0.95 + it * 0.2));

                    accumA = lerp(accumA, sA, _Diffusion * 0.6);
                    accumB = lerp(accumB, b0, _Diffusion * 0.55);
                    accumC = lerp(accumC, c0, _Diffusion * 0.5);

                    // slightly advect accumulators too to keep motion
                    accumA += (fbm(screenUV * (_Scale*0.5 + it*0.2) + t*0.3) - 0.5) * 0.02;
                    accumB += (fbm((screenUV+0.12) * (_Scale*0.55 + it*0.2) + t*0.25) - 0.5) * 0.02;
                    accumC += (fbm((screenUV-0.09) * (_Scale*0.45 + it*0.2) + t*0.28) - 0.5) * 0.02;
                }

                accumA = saturate(accumA);
                accumB = saturate(accumB);
                accumC = saturate(accumC);

                // color channels multiplied by their color
                float3 colA = _ColorA.rgb * accumA;
                float3 colB = _ColorB.rgb * accumB;
                float3 colC = _ColorC.rgb * accumC;

                // simulate watercolor subtractive mixing by mixing in CMY space pairwise
                float3 mixAB = subtractiveMix(colA, colB);
                float3 mixABC = subtractiveMix(mixAB, colC);

                // combine with some remaining individual highlights for luminosity
                float3 final = mixABC * (0.6 + 0.8 * (accumA + accumB + accumC) / 3.0);

                // add soft glow from concentrated blobs
                float3 blobs = float3(0,0,0);
                float2 bPos0 = float2(sin(t * 0.6) * 0.5, cos(t * 0.4) * 0.35);
                float2 bPos1 = float2(cos(t * 0.9) * -0.35, sin(t * 0.7) * 0.45 + 0.05);
                float2 bPos2 = float2(sin(t * 0.3 + 1.0) * 0.28, sin(t * 0.5 + 2.0) * -0.27);

                float b0 = exp(-dot(uv - bPos0, uv - bPos0) * 12.0) * (0.5 + 0.5 * sin(t * 1.2));
                float b1 = exp(-dot(uv - bPos1, uv - bPos1) * 14.0) * (0.45 + 0.55 * sin(t * 0.9 + 0.6));
                float b2 = exp(-dot(uv - bPos2, uv - bPos2) * 11.0) * (0.35 + 0.65 * sin(t * 0.7 + 1.3));

                blobs += b0 * _ColorA.rgb;
                blobs += b1 * _ColorB.rgb;
                blobs += b2 * _ColorC.rgb;

                final += blobs * (_Glow * 0.6);

                // blend with low-frequency base shape to make large smooth blobs
                // use palette between colors based on baseShape
                float4 baseCol = lerp(_ColorA, _ColorB, baseShape);
                baseCol = lerp(baseCol, _ColorC, smoothstep(0.4, 0.8, baseShape));

                final = lerp(final, baseCol.rgb * (0.7 + 0.8 * baseShape), _ShapeWeight * shapeMask);

                // vignette and tonemapping
                float vign = smoothstep(0.8, _Vignette, length(uv));
                final *= lerp(1.0, 0.55, vign);

                // gamma and contrast
                final = pow(final, 1.0 / 2.2);
                final = lerp(0.45 * final, final, _Contrast);

                return float4(saturate(final), 1.0);
            }
            ENDCG
        }
    }

    FallBack "Unlit/Color"
}
