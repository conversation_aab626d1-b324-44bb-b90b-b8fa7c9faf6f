# Unity Fluid Simulation for UI Raw Image

This project contains a complete GPU-based fluid simulation system that can be displayed on Unity UI Raw Images, similar to the WebGL fluid simulation you referenced.

## Features

- **GPU-Accelerated**: Uses compute shaders for high-performance fluid simulation
- **Idle Animation**: Automatically generates colorful splats to keep the fluid moving
- **Interactive**: Click or touch to add splats and interact with the fluid
- **UI Integration**: Designed specifically for Unity UI Raw Images
- **Customizable**: Many parameters to adjust the simulation behavior

## Quick Start

### Method 1: Automatic Setup
1. Create an empty scene in Unity
2. Add the `FluidSceneSetup` script to any GameObject
3. In the inspector, check "Setup On Start"
4. Play the scene - everything will be set up automatically!

### Method 2: Manual Setup
1. Create a Canvas with a Raw Image component
2. Add the `FluidUIDemo` script to a GameObject
3. Assign the Raw Image to the `fluidRawImage` field
4. Assign the compute shader and materials:
   - `fluidCompute`: Assets/Shaders/Fluid.compute
   - `fluidBlitMaterial`: Create material using Hidden/FluidBlit shader
   - `fluidUnlitMaterial`: Create material using Custom/FluidWatercolorUnlit shader
5. Play the scene

## Components

### FluidSim.cs
The core fluid simulation component that:
- Manages render textures for velocity, dye, and pressure fields
- Runs the compute shader kernels for fluid physics
- Handles automatic idle splat generation
- Outputs to a Raw Image for UI display

### FluidUIDemo.cs
A demo wrapper that:
- Automatically sets up the fluid simulation for UI display
- Handles user input (mouse/touch) for interactive splats
- Provides inspector controls for real-time parameter adjustment

### FluidSceneSetup.cs
A utility script that:
- Automatically creates the complete UI hierarchy
- Finds and assigns necessary resources
- Sets up proper Canvas and EventSystem

### Fluid.compute
The compute shader containing kernels for:
- Velocity advection (semi-Lagrangian)
- Dye advection
- Splat injection
- Divergence computation
- Jacobi pressure solving
- Gradient subtraction

### Shaders
- **FluidBlit.shader**: Post-processing shader for tone mapping
- **FluidUnlit.shader**: Beautiful watercolor-style rendering shader

## Parameters

### Simulation Settings
- **Resolution**: Simulation texture resolution (128-512)
- **Velocity Dissipation**: How quickly velocity fades (0.001-0.1)
- **Dye Dissipation**: How quickly colors fade (0.001-0.1)
- **Splat Radius**: Size of splats (0.01-0.2)
- **Jacobi Iterations**: Pressure solver accuracy (10-50)

### Idle Animation
- **Enable Idle Animation**: Toggle automatic splats
- **Idle Splat Interval**: Time between automatic splats (0.1-3.0s)
- **Color Cycle Speed**: Speed of color changes (0.5-3.0)
- **Force Strength**: Strength of splat forces (5-30)

## Performance Notes

- The simulation runs entirely on the GPU using compute shaders
- Higher resolutions (512x512) may impact performance on mobile devices
- The system is optimized for real-time performance with minimal CPU overhead
- Render textures are properly managed and released to prevent memory leaks

## Compatibility

- Requires Unity 2019.4 or later
- Requires compute shader support (most modern GPUs)
- Works on PC, Mac, and mobile devices with compute shader support
- Tested with Unity UI Canvas in Screen Space - Overlay mode

## Customization

You can easily customize the visual appearance by:
1. Modifying the FluidUnlit.shader for different visual styles
2. Adjusting the color palette in the shader properties
3. Changing the noise functions for different fluid behaviors
4. Modifying the idle splat generation patterns in FluidSim.cs

## Troubleshooting

**Black screen or no fluid visible:**
- Check that compute shaders are supported on your platform
- Ensure the Fluid.compute shader is assigned
- Verify that the Raw Image has the correct material assigned

**Poor performance:**
- Reduce the simulation resolution
- Decrease the number of Jacobi iterations
- Disable idle animation if not needed

**Compilation errors:**
- Ensure you're using Unity 2019.4 or later
- Check that all shader files are properly imported

## Similar to WebGL Fluid Simulation

This Unity implementation provides similar visual effects to the WebGL fluid simulation you referenced:
- Smooth, flowing fluid motion
- Colorful dye injection and mixing
- Interactive splat generation
- Real-time GPU-based simulation
- Beautiful visual rendering with proper color blending

The main difference is that this version is optimized for Unity UI and includes additional features like automatic idle animation and easy integration with Unity's UI system.