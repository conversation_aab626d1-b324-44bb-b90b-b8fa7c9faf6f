Shader "Hidden/FluidBlit"
{
    Properties 
    { 
        _MainTex ("MainTex", 2D) = "white" {}
        _Intensity ("Intensity", Float) = 1.5
        _Contrast ("Contrast", Float) = 1.2
        _Brightness ("Brightness", Float) = 1.1
    }
    SubShader
    {
        Tags { "RenderType"="Opaque" }
        Pass
        {
            ZTest Always Cull Off ZWrite Off
            CGPROGRAM
            #pragma vertex vert
            #pragma fragment frag
            #include "UnityCG.cginc"

            sampler2D _MainTex;
            float4 _MainTex_TexelSize;
            float _Intensity;
            float _Contrast;
            float _Brightness;

            struct appdata { float4 vertex : POSITION; float2 uv : TEXCOORD0; };
            struct v2f { float2 uv : TEXCOORD0; float4 vertex : SV_POSITION; };

            v2f vert(appdata v) 
            { 
                v2f o; 
                o.vertex = UnityObjectToClipPos(v.vertex); 
                o.uv = v.uv; 
                return o; 
            }

            fixed4 frag(v2f i) : SV_Target
            {
                fixed4 c = tex2D(_MainTex, i.uv);
                
                // Boost the intensity of the fluid colors
                c.rgb *= _Intensity;
                
                // Apply brightness
                c.rgb *= _Brightness;
                
                // Apply contrast
                c.rgb = ((c.rgb - 0.5) * _Contrast) + 0.5;
                
                // Gamma correction for better display
                c.rgb = pow(abs(c.rgb), 1.0/2.2);
                
                // Ensure alpha is visible
                c.a = max(c.a, length(c.rgb) * 0.5);
                
                return saturate(c);
            }
            ENDCG
        }
    }
    FallBack Off
}