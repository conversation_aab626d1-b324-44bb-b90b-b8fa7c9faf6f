using UnityEngine;
using UnityEngine.UI;
using UnityEngine.EventSystems;

/// <summary>
/// Setup for the smooth fluid simulation
/// </summary>
public class SmoothFluidSetup : MonoBehaviour
{
    [Header("Auto Setup")]
    public bool setupOnStart = true;
    
    void Start()
    {
        if (setupOnStart)
        {
            SetupSmoothFluidDemo();
        }
    }
    
    [ContextMenu("Setup Smooth Fluid Demo")]
    public void SetupSmoothFluidDemo()
    {
        // Create canvas
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null)
        {
            GameObject canvasGO = new GameObject("Canvas");
            canvas = canvasGO.AddComponent<Canvas>();
            canvas.renderMode = RenderMode.ScreenSpaceOverlay;
            canvasGO.AddComponent<CanvasScaler>();
            canvasGO.AddComponent<GraphicRaycaster>();
        }
        
        // Create EventSystem
        if (FindObjectOfType<EventSystem>() == null)
        {
            GameObject eventSystemGO = new GameObject("EventSystem");
            eventSystemGO.AddComponent<EventSystem>();
            eventSystemGO.AddComponent<StandaloneInputModule>();
        }
        
        // Create main panel
        GameObject panelGO = new GameObject("SmoothFluidPanel");
        panelGO.transform.SetParent(canvas.transform, false);
        
        Image panelImage = panelGO.AddComponent<Image>();
        panelImage.color = new Color(0.05f, 0.05f, 0.1f, 1f); // Dark blue background
        
        RectTransform panelRect = panelGO.GetComponent<RectTransform>();
        panelRect.anchorMin = Vector2.zero;
        panelRect.anchorMax = Vector2.one;
        panelRect.offsetMin = Vector2.zero;
        panelRect.offsetMax = Vector2.zero;
        
        // Create Raw Image for fluid display
        GameObject rawImageGO = new GameObject("SmoothFluidRawImage");
        rawImageGO.transform.SetParent(panelGO.transform, false);
        
        RawImage rawImage = rawImageGO.AddComponent<RawImage>();
        rawImage.color = Color.white;
        
        RectTransform rawImageRect = rawImageGO.GetComponent<RectTransform>();
        rawImageRect.anchorMin = new Vector2(0.5f, 0.5f);
        rawImageRect.anchorMax = new Vector2(0.5f, 0.5f);
        rawImageRect.anchoredPosition = Vector2.zero;
        rawImageRect.sizeDelta = new Vector2(600, 600); // Larger for better visibility
        
        // Create title
        GameObject titleGO = new GameObject("Title");
        titleGO.transform.SetParent(panelGO.transform, false);
        
        Text titleText = titleGO.AddComponent<Text>();
        titleText.text = "SMOOTH FLUID SIMULATION";
        titleText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        titleText.fontSize = 28;
        titleText.color = Color.white;
        titleText.alignment = TextAnchor.MiddleCenter;
        titleText.fontStyle = FontStyle.Bold;
        
        RectTransform titleRect = titleGO.GetComponent<RectTransform>();
        titleRect.anchorMin = new Vector2(0f, 1f);
        titleRect.anchorMax = new Vector2(1f, 1f);
        titleRect.anchoredPosition = new Vector2(0, -30);
        titleRect.sizeDelta = new Vector2(0, 60);
        
        // Create instructions
        GameObject instructionsGO = new GameObject("Instructions");
        instructionsGO.transform.SetParent(panelGO.transform, false);
        
        Text instructionsText = instructionsGO.AddComponent<Text>();
        instructionsText.text = "Advanced GPU fluid simulation with smooth motion and realistic blending.\n" +
                               "• Drag mouse to create interactive splats\n" +
                               "• Automatic idle animation with swirling patterns\n" +
                               "• Multi-step advection for smooth motion\n" +
                               "• Curl forces for natural vorticity\n" +
                               "• Dye diffusion for realistic color mixing";
        instructionsText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        instructionsText.fontSize = 16;
        instructionsText.color = new Color(0.9f, 0.9f, 0.9f, 1f);
        instructionsText.alignment = TextAnchor.MiddleCenter;
        
        RectTransform instructionsRect = instructionsGO.GetComponent<RectTransform>();
        instructionsRect.anchorMin = new Vector2(0f, 0f);
        instructionsRect.anchorMax = new Vector2(1f, 0f);
        instructionsRect.anchoredPosition = new Vector2(0, 80);
        instructionsRect.sizeDelta = new Vector2(-40, 160);
        
        // Find the smooth fluid compute shader
        ComputeShader smoothFluidCompute = FindSmoothFluidCompute();
        
        if (smoothFluidCompute == null)
        {
            Debug.LogError("SmoothFluidSetup: Could not find SmoothFluid.compute shader!");
            
            // Create error message
            GameObject errorGO = new GameObject("ErrorMessage");
            errorGO.transform.SetParent(panelGO.transform, false);
            
            Text errorText = errorGO.AddComponent<Text>();
            errorText.text = "ERROR: SmoothFluid.compute shader not found!\n" +
                           "Please ensure the SmoothFluid.compute file is in the project.";
            errorText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
            errorText.fontSize = 18;
            errorText.color = Color.red;
            errorText.alignment = TextAnchor.MiddleCenter;
            
            RectTransform errorRect = errorGO.GetComponent<RectTransform>();
            errorRect.anchorMin = new Vector2(0.2f, 0.4f);
            errorRect.anchorMax = new Vector2(0.8f, 0.6f);
            errorRect.offsetMin = Vector2.zero;
            errorRect.offsetMax = Vector2.zero;
            
            return;
        }
        
        // Add the smooth fluid simulation component
        SmoothFluidSim smoothFluid = panelGO.AddComponent<SmoothFluidSim>();
        smoothFluid.targetRawImage = rawImage;
        smoothFluid.smoothFluidCompute = smoothFluidCompute;
        
        // Configure for optimal quality and vibrant color mixing
        smoothFluid.resolution = 256;
        smoothFluid.substeps = 2;
        smoothFluid.jacobiIterations = 30;
        smoothFluid.dyeDissipation = 0.00005f;    // Very low dissipation for persistent colors
        smoothFluid.velocityDissipation = 0.0002f; // Low velocity dissipation for smooth motion
        smoothFluid.dyeDiffusion = 0.002f;         // Higher diffusion for better color mixing
        smoothFluid.curlStrength = 0.5f;           // Stronger curl for more interesting motion
        smoothFluid.splatRadius = 0.15f;           // Larger splats for better coverage
        smoothFluid.colorIntensity = 1.8f;         // Moderate intensity to allow mixing
        smoothFluid.forceMultiplier = 60f;         // Stronger forces for more dynamic motion
        smoothFluid.enableIdleAnimation = true;
        smoothFluid.idleSplatInterval = 0.4f;      // More frequent splats
        smoothFluid.idleForceStrength = 2.0f;      // Stronger idle forces
        
        Debug.Log("SmoothFluidSetup: Advanced fluid simulation setup complete!");
        Debug.Log("This simulation features:");
        Debug.Log("- Multi-step advection for smooth motion");
        Debug.Log("- Curl forces for natural vorticity");
        Debug.Log("- Dye diffusion for realistic mixing");
        Debug.Log("- High-quality Jacobi pressure solving");
        Debug.Log("- Interactive mouse/touch controls");
    }
    
    ComputeShader FindSmoothFluidCompute()
    {
        // Try to find the SmoothFluid compute shader
        ComputeShader compute = null;
        
        // Try Resources first
        compute = Resources.Load<ComputeShader>("SmoothFluid");
        if (compute != null) return compute;
        
        // Try to find in project
#if UNITY_EDITOR
        string[] guids = UnityEditor.AssetDatabase.FindAssets("SmoothFluid t:ComputeShader");
        if (guids.Length > 0)
        {
            string path = UnityEditor.AssetDatabase.GUIDToAssetPath(guids[0]);
            compute = UnityEditor.AssetDatabase.LoadAssetAtPath<ComputeShader>(path);
        }
#endif
        
        return compute;
    }
}