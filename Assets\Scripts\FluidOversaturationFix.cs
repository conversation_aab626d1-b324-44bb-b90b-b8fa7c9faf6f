using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Monitors fluid simulation for oversaturation and provides automatic fixes
/// Helps prevent the "white screen" issue by detecting and correcting color accumulation
/// </summary>
public class FluidOversaturationFix : MonoBehaviour
{
    [Header("Monitoring")]
    public bool enableAutoFix = true;
    public float checkInterval = 2.0f;  // Check every 2 seconds
    public float oversaturationThreshold = 0.8f;  // Threshold for detecting oversaturation
    
    [Header("Fix Actions")]
    public bool enableDissipationBoost = true;
    public bool enableColorReset = true;
    public bool enableParameterAdjustment = true;
    
    [Header("Debug")]
    public bool showDebugInfo = true;
    public Text debugText;
    
    private EnhancedFluidSim enhancedFluidSim;
    private FluidSim fluidSim;
    private float lastCheckTime;
    private int oversaturationCount = 0;
    
    void Start()
    {
        // Find fluid simulation components
        enhancedFluidSim = FindObjectOfType<EnhancedFluidSim>();
        fluidSim = FindObjectOfType<FluidSim>();
        
        if (enhancedFluidSim == null && fluidSim == null)
        {
            Debug.LogWarning("FluidOversaturationFix: No fluid simulation found!");
            enabled = false;
            return;
        }
        
        CreateDebugUI();
        Debug.Log("FluidOversaturationFix: Monitoring system initialized");
    }
    
    void Update()
    {
        if (!enableAutoFix) return;
        
        // Check for oversaturation periodically
        if (Time.time - lastCheckTime >= checkInterval)
        {
            lastCheckTime = Time.time;
            CheckForOversaturation();
        }
        
        // Update debug info
        if (showDebugInfo && debugText != null)
        {
            UpdateDebugInfo();
        }
    }
    
    void CheckForOversaturation()
    {
        RenderTexture dyeTexture = GetDyeTexture();
        if (dyeTexture == null) return;
        
        // Sample the texture to check for oversaturation
        float averageIntensity = SampleTextureIntensity(dyeTexture);
        
        if (averageIntensity > oversaturationThreshold)
        {
            oversaturationCount++;
            Debug.LogWarning($"FluidOversaturationFix: Oversaturation detected! Average intensity: {averageIntensity:F3}");
            
            ApplyOversaturationFix();
        }
        else
        {
            // Reset counter if no oversaturation
            if (oversaturationCount > 0)
            {
                oversaturationCount = Mathf.Max(0, oversaturationCount - 1);
            }
        }
    }
    
    RenderTexture GetDyeTexture()
    {
        if (enhancedFluidSim != null)
        {
            // Access the dye texture through reflection if needed
            var field = typeof(EnhancedFluidSim).GetField("dyeRT", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return field?.GetValue(enhancedFluidSim) as RenderTexture;
        }
        else if (fluidSim != null)
        {
            var field = typeof(FluidSim).GetField("dyeRT", 
                System.Reflection.BindingFlags.NonPublic | System.Reflection.BindingFlags.Instance);
            return field?.GetValue(fluidSim) as RenderTexture;
        }
        return null;
    }
    
    float SampleTextureIntensity(RenderTexture texture)
    {
        // Create a temporary texture to read from GPU
        RenderTexture.active = texture;
        Texture2D tempTexture = new Texture2D(texture.width, texture.height, TextureFormat.RGBAFloat, false);
        tempTexture.ReadPixels(new Rect(0, 0, texture.width, texture.height), 0, 0);
        tempTexture.Apply();
        RenderTexture.active = null;
        
        // Sample a few pixels to check intensity
        float totalIntensity = 0f;
        int sampleCount = 0;
        int step = Mathf.Max(1, texture.width / 16); // Sample every 16th pixel
        
        for (int y = 0; y < texture.height; y += step)
        {
            for (int x = 0; x < texture.width; x += step)
            {
                Color pixel = tempTexture.GetPixel(x, y);
                float intensity = (pixel.r + pixel.g + pixel.b) / 3f;
                totalIntensity += intensity;
                sampleCount++;
            }
        }
        
        DestroyImmediate(tempTexture);
        
        return sampleCount > 0 ? totalIntensity / sampleCount : 0f;
    }
    
    void ApplyOversaturationFix()
    {
        if (enableDissipationBoost)
        {
            BoostDissipation();
        }
        
        if (enableColorReset && oversaturationCount > 3)
        {
            ResetFluidColors();
        }
        
        if (enableParameterAdjustment)
        {
            AdjustSimulationParameters();
        }
    }
    
    void BoostDissipation()
    {
        if (enhancedFluidSim != null)
        {
            enhancedFluidSim.dyeDissipation = Mathf.Min(0.02f, enhancedFluidSim.dyeDissipation * 1.5f);
            enhancedFluidSim.idleIntensity = Mathf.Max(0.1f, enhancedFluidSim.idleIntensity * 0.8f);
            Debug.Log("FluidOversaturationFix: Boosted dissipation for EnhancedFluidSim");
        }
        else if (fluidSim != null)
        {
            fluidSim.dyeDissipation = Mathf.Min(0.01f, fluidSim.dyeDissipation * 1.5f);
            fluidSim.idleSplatIntensity = Mathf.Max(0.3f, fluidSim.idleSplatIntensity * 0.8f);
            Debug.Log("FluidOversaturationFix: Boosted dissipation for FluidSim");
        }
    }
    
    void ResetFluidColors()
    {
        RenderTexture dyeTexture = GetDyeTexture();
        if (dyeTexture != null)
        {
            // Clear the dye texture
            Graphics.Blit(Texture2D.blackTexture, dyeTexture);
            oversaturationCount = 0;
            Debug.Log("FluidOversaturationFix: Reset fluid colors");
        }
    }
    
    void AdjustSimulationParameters()
    {
        if (enhancedFluidSim != null)
        {
            // Reduce idle animation intensity
            enhancedFluidSim.idleIntensity = Mathf.Max(0.05f, enhancedFluidSim.idleIntensity * 0.9f);
            enhancedFluidSim.idleAnimationSpeed = Mathf.Max(0.5f, enhancedFluidSim.idleAnimationSpeed * 0.95f);
        }
        else if (fluidSim != null)
        {
            // Increase splat interval and reduce intensity
            fluidSim.idleSplatInterval = Mathf.Min(2.0f, fluidSim.idleSplatInterval * 1.1f);
            fluidSim.idleSplatIntensity = Mathf.Max(0.2f, fluidSim.idleSplatIntensity * 0.9f);
        }
    }
    
    void CreateDebugUI()
    {
        if (!showDebugInfo) return;
        
        Canvas canvas = FindObjectOfType<Canvas>();
        if (canvas == null) return;
        
        // Create debug text
        GameObject debugGO = new GameObject("FluidDebugInfo");
        debugGO.transform.SetParent(canvas.transform, false);
        
        debugText = debugGO.AddComponent<Text>();
        debugText.text = "Fluid Monitor: OK";
        debugText.font = Resources.GetBuiltinResource<Font>("LegacyRuntime.ttf");
        debugText.fontSize = 12;
        debugText.color = Color.green;
        debugText.alignment = TextAnchor.UpperLeft;
        
        RectTransform debugRect = debugGO.GetComponent<RectTransform>();
        debugRect.anchorMin = new Vector2(0, 0);
        debugRect.anchorMax = new Vector2(0, 0);
        debugRect.anchoredPosition = new Vector2(10, 50);
        debugRect.sizeDelta = new Vector2(300, 100);
    }
    
    void UpdateDebugInfo()
    {
        if (debugText == null) return;
        
        string status = oversaturationCount > 0 ? "WARNING" : "OK";
        Color statusColor = oversaturationCount > 0 ? Color.yellow : Color.green;
        
        if (oversaturationCount > 3)
        {
            status = "CRITICAL";
            statusColor = Color.red;
        }
        
        debugText.color = statusColor;
        debugText.text = $"Fluid Monitor: {status}\n" +
                        $"Oversaturation Count: {oversaturationCount}\n" +
                        $"Auto-fix: {(enableAutoFix ? "ON" : "OFF")}\n" +
                        $"Last Check: {Time.time - lastCheckTime:F1}s ago";
    }
    
    // Public methods for manual control
    [ContextMenu("Force Reset Colors")]
    public void ForceResetColors()
    {
        ResetFluidColors();
    }
    
    [ContextMenu("Force Parameter Adjustment")]
    public void ForceParameterAdjustment()
    {
        AdjustSimulationParameters();
    }
    
    [ContextMenu("Toggle Auto Fix")]
    public void ToggleAutoFix()
    {
        enableAutoFix = !enableAutoFix;
        Debug.Log($"FluidOversaturationFix: Auto-fix {(enableAutoFix ? "enabled" : "disabled")}");
    }
}
