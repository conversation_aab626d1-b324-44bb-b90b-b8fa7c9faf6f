// Enhanced GPU fluid simulation with advanced idle animation features
// Includes multiple animation patterns, improved color mixing, and performance optimizations

#pragma kernel AdvectVelocity
#pragma kernel AdvectDye
#pragma kernel AddSplat
#pragma kernel AddMultiSplat
#pragma kernel ComputeDivergence
#pragma kernel Jacobi
#pragma kernel SubtractGradient
#pragma kernel AddCurlForce
#pragma kernel GenerateIdlePattern
#pragma kernel DiffuseDye
#pragma target 5.0

// External parameters
int2 _SimResolution;
float _TimeStep;
float _Dissipation;
float _VelocityDissipation;
float _PressureDissipation;
float _Radius;
float4 _SplatColor;
float2 _SplatPos;
float2 _SplatForce;

// Enhanced idle animation parameters
float _IdleTime;
float _PatternScale;
float _ColorCycleSpeed;
int _PatternType; // 0=spiral, 1=wave, 2=orbit, 3=perlin
float _SpiralSpeed;
float _WaveAmplitude;
float _OrbitRadius;
float _IdleIntensity;

// Density diffusion parameters
float _DensityDiffusion;

// Textures
RWTexture2D<float4> _Velocity;
RWTexture2D<float4> _VelocityOut;
RWTexture2D<float4> _Dye;
RWTexture2D<float4> _DyeOut;
RWTexture2D<float> _Divergence;
RWTexture2D<float> _Pressure;
RWTexture2D<float> _Curl;

// Utility functions
float2 bilerp(RWTexture2D<float4> tex, float2 uv)
{
    float2 size = float2(_SimResolution);
    float2 coord = uv * size - 0.5;
    int2 i = int2(floor(coord));
    float2 f = frac(coord);
    
    float2 a = tex[clamp(i, 0, _SimResolution - 1)].xy;
    float2 b = tex[clamp(i + int2(1, 0), 0, _SimResolution - 1)].xy;
    float2 c = tex[clamp(i + int2(0, 1), 0, _SimResolution - 1)].xy;
    float2 d = tex[clamp(i + int2(1, 1), 0, _SimResolution - 1)].xy;
    
    return lerp(lerp(a, b, f.x), lerp(c, d, f.x), f.y);
}

float4 bilerpDye(RWTexture2D<float4> tex, float2 uv)
{
    float2 size = float2(_SimResolution);
    float2 coord = uv * size - 0.5;
    int2 i = int2(floor(coord));
    float2 f = frac(coord);
    
    float4 a = tex[clamp(i, 0, _SimResolution - 1)];
    float4 b = tex[clamp(i + int2(1, 0), 0, _SimResolution - 1)];
    float4 c = tex[clamp(i + int2(0, 1), 0, _SimResolution - 1)];
    float4 d = tex[clamp(i + int2(1, 1), 0, _SimResolution - 1)];
    
    return lerp(lerp(a, b, f.x), lerp(c, d, f.x), f.y);
}

// Improved noise function for better patterns
float noise(float2 p)
{
    return frac(sin(dot(p, float2(12.9898, 78.233))) * 43758.5453);
}

float smoothNoise(float2 p)
{
    float2 i = floor(p);
    float2 f = frac(p);
    f = f * f * (3.0 - 2.0 * f); // Smooth interpolation

    float a = noise(i);
    float b = noise(i + float2(1.0, 0.0));
    float c = noise(i + float2(0.0, 1.0));
    float d = noise(i + float2(1.0, 1.0));

    return lerp(lerp(a, b, f.x), lerp(c, d, f.x), f.y);
}

// HSV to RGB conversion for proper color handling
float3 hsv2rgb(float3 hsv)
{
    float4 K = float4(1.0, 2.0 / 3.0, 1.0 / 3.0, 3.0);
    float3 p = abs(frac(hsv.xxx + K.xyz) * 6.0 - K.www);
    return hsv.z * lerp(K.xxx, saturate(p - K.xxx), hsv.y);
}

// Advect velocity with improved stability
[numthreads(8,8,1)]
void AdvectVelocity(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float2 uv = (float2(pix) + 0.5) / float2(_SimResolution);
    float2 vel = _Velocity[pix].xy;
    
    // Backward advection with improved stability
    float2 prevUV = uv - vel * _TimeStep / float2(_SimResolution);
    float2 advectedVel = bilerp(_Velocity, prevUV);
    
    // Apply dissipation
    advectedVel *= (1.0 - _VelocityDissipation * _TimeStep);
    
    _VelocityOut[pix] = float4(advectedVel, 0, 0);
}

// Advect dye with enhanced color preservation
[numthreads(8,8,1)]
void AdvectDye(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float2 uv = (float2(pix) + 0.5) / float2(_SimResolution);
    float2 vel = _Velocity[pix].xy;
    
    // Backward advection
    float2 prevUV = uv - vel * _TimeStep / float2(_SimResolution);
    float4 advectedDye = bilerpDye(_Dye, prevUV);
    
    // Apply dissipation with color preservation
    advectedDye.rgb *= (1.0 - _Dissipation * _TimeStep);
    advectedDye.a = 1.0;
    
    _DyeOut[pix] = advectedDye;
}

// Enhanced splat with better color mixing and oversaturation prevention
[numthreads(8,8,1)]
void AddSplat(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float2 uv = (float2(pix) + 0.5) / float2(_SimResolution);
    float2 diff = uv - _SplatPos;
    float dist2 = dot(diff, diff);
    float r = _Radius;

    // Smooth gaussian falloff
    float w = exp(-dist2 / (r * r * 0.5));

    if (w > 0.01)
    {
        // Enhanced dye mixing with oversaturation prevention
        float4 baseDye = _Dye[pix];
        float4 splatColor = float4(_SplatColor.rgb * w, 1.0);

        // Reduce intensity based on existing color to prevent oversaturation
        float existingIntensity = length(baseDye.rgb);
        float intensityReduction = saturate(1.0 - existingIntensity * 0.5);

        // Improved color blending with adaptive intensity
        float4 mixedDye = baseDye + splatColor * 0.3 * intensityReduction;

        // More aggressive clamping to prevent white oversaturation
        mixedDye.rgb = min(mixedDye.rgb, float3(1.5, 1.5, 1.5));
        mixedDye.a = 1.0;

        _DyeOut[pix] = mixedDye;

        // Enhanced velocity addition
        float2 baseVel = _Velocity[pix].xy;
        float2 addedVel = _SplatForce * w;
        _VelocityOut[pix] = float4(baseVel + addedVel, 0, 0);
    }
    else
    {
        _DyeOut[pix] = _Dye[pix];
        _VelocityOut[pix] = _Velocity[pix];
    }
}

// Multi-splat kernel for efficient batch processing
[numthreads(8,8,1)]
void AddMultiSplat(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float2 uv = (float2(pix) + 0.5) / float2(_SimResolution);
    float4 baseDye = _Dye[pix];
    float2 baseVel = _Velocity[pix].xy;
    
    // Process multiple splats in one kernel call
    // This would be expanded with actual splat data from a buffer
    
    _DyeOut[pix] = baseDye;
    _VelocityOut[pix] = float4(baseVel, 0, 0);
}

// Compute divergence
[numthreads(8,8,1)]
void ComputeDivergence(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float2 vL = _Velocity[clamp(pix + int2(-1, 0), 0, _SimResolution - 1)].xy;
    float2 vR = _Velocity[clamp(pix + int2(1, 0), 0, _SimResolution - 1)].xy;
    float2 vB = _Velocity[clamp(pix + int2(0, -1), 0, _SimResolution - 1)].xy;
    float2 vT = _Velocity[clamp(pix + int2(0, 1), 0, _SimResolution - 1)].xy;

    float div = 0.5 * ((vR.x - vL.x) + (vT.y - vB.y));
    _Divergence[pix] = div;
}

// Jacobi iteration for pressure solving
[numthreads(8,8,1)]
void Jacobi(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float pL = _Pressure[clamp(pix + int2(-1, 0), 0, _SimResolution - 1)];
    float pR = _Pressure[clamp(pix + int2(1, 0), 0, _SimResolution - 1)];
    float pB = _Pressure[clamp(pix + int2(0, -1), 0, _SimResolution - 1)];
    float pT = _Pressure[clamp(pix + int2(0, 1), 0, _SimResolution - 1)];
    float div = _Divergence[pix];

    float newPressure = (pL + pR + pB + pT - div) * 0.25;
    newPressure *= (1.0 - _PressureDissipation * _TimeStep);
    
    _Pressure[pix] = newPressure;
}

// Subtract pressure gradient
[numthreads(8,8,1)]
void SubtractGradient(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float pL = _Pressure[clamp(pix + int2(-1, 0), 0, _SimResolution - 1)];
    float pR = _Pressure[clamp(pix + int2(1, 0), 0, _SimResolution - 1)];
    float pB = _Pressure[clamp(pix + int2(0, -1), 0, _SimResolution - 1)];
    float pT = _Pressure[clamp(pix + int2(0, 1), 0, _SimResolution - 1)];

    float2 gradient = 0.5 * float2(pR - pL, pT - pB);
    float2 vel = _Velocity[pix].xy;
    
    _VelocityOut[pix] = float4(vel - gradient, 0, 0);
}

// Add curl forces for more realistic turbulence
[numthreads(8,8,1)]
void AddCurlForce(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    // Compute curl
    float2 vL = _Velocity[clamp(pix + int2(-1, 0), 0, _SimResolution - 1)].xy;
    float2 vR = _Velocity[clamp(pix + int2(1, 0), 0, _SimResolution - 1)].xy;
    float2 vB = _Velocity[clamp(pix + int2(0, -1), 0, _SimResolution - 1)].xy;
    float2 vT = _Velocity[clamp(pix + int2(0, 1), 0, _SimResolution - 1)].xy;

    float curl = 0.5 * ((vR.y - vL.y) - (vT.x - vB.x));
    _Curl[pix] = curl;

    // Compute curl gradient
    float cL = _Curl[clamp(pix + int2(-1, 0), 0, _SimResolution - 1)];
    float cR = _Curl[clamp(pix + int2(1, 0), 0, _SimResolution - 1)];
    float cB = _Curl[clamp(pix + int2(0, -1), 0, _SimResolution - 1)];
    float cT = _Curl[clamp(pix + int2(0, 1), 0, _SimResolution - 1)];

    float2 curlGrad = 0.5 * float2(cR - cL, cT - cB);
    float curlMag = length(curlGrad);
    
    if (curlMag > 0.0001)
    {
        curlGrad /= curlMag;
        float2 curlForce = curlGrad * curl * 0.1; // Curl strength
        
        float2 vel = _Velocity[pix].xy;
        _VelocityOut[pix] = float4(vel + curlForce * _TimeStep, 0, 0);
    }
    else
    {
        _VelocityOut[pix] = _Velocity[pix];
    }
}

// Generate procedural idle animation patterns
[numthreads(8,8,1)]
void GenerateIdlePattern(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float2 uv = (float2(pix) + 0.5) / float2(_SimResolution);
    float2 center = float2(0.5, 0.5);
    float2 toCenter = center - uv;
    float dist = length(toCenter);
    
    float4 baseDye = _Dye[pix];
    float2 baseVel = _Velocity[pix].xy;
    
    float intensity = 0.0;
    float2 force = float2(0, 0);
    float3 color = float3(0, 0, 0);
    
    // Pattern generation based on type with reduced intensity
    if (_PatternType == 0) // Spiral
    {
        float angle = atan2(toCenter.y, toCenter.x) + _IdleTime * _SpiralSpeed;
        float spiral = sin(angle * 3.0 + dist * 10.0 - _IdleTime * 2.0);
        intensity = smoothstep(0.3, 0.7, spiral) * 0.02; // Reduced from 0.1 to 0.02

        float2 tangent = float2(-toCenter.y, toCenter.x);
        force = normalize(tangent) * intensity * 15.0; // Reduced force

        float hue = frac(angle / (2.0 * 3.14159) + _IdleTime * _ColorCycleSpeed);
        color = float3(hue, 0.6, 0.8); // Reduced saturation and value
    }
    else if (_PatternType == 1) // Wave
    {
        float wave = sin(uv.x * 8.0 + _IdleTime * 2.0) * _WaveAmplitude;
        intensity = exp(-abs(uv.y - 0.5 - wave) * 10.0) * 0.02; // Reduced from 0.1 to 0.02

        force = float2(15.0, wave * 20.0) * intensity; // Reduced force

        float hue = frac(uv.x + _IdleTime * _ColorCycleSpeed);
        color = float3(hue, 0.6, 0.8); // Reduced saturation and value
    }
    else if (_PatternType == 2) // Orbit
    {
        float angle = _IdleTime * 0.8;
        float2 orbitCenter = center + float2(cos(angle), sin(angle)) * _OrbitRadius;
        float orbitDist = length(uv - orbitCenter);
        intensity = exp(-orbitDist * 15.0) * 0.02; // Reduced from 0.1 to 0.02

        float2 orbitTangent = float2(-sin(angle), cos(angle));
        force = orbitTangent * intensity * 20.0; // Reduced force

        float hue = frac(_IdleTime * _ColorCycleSpeed);
        color = float3(hue, 0.65, 0.8); // Reduced saturation and value
    }
    else if (_PatternType == 3) // Perlin noise
    {
        float noise1 = smoothNoise(uv * 4.0 + _IdleTime * 0.5);
        float noise2 = smoothNoise(uv * 8.0 + _IdleTime * 0.3);
        intensity = (noise1 + noise2 * 0.5) * 0.01; // Reduced from 0.05 to 0.01

        float2 noiseGrad = float2(
            smoothNoise(uv * 4.0 + float2(0.01, 0) + _IdleTime * 0.5) - noise1,
            smoothNoise(uv * 4.0 + float2(0, 0.01) + _IdleTime * 0.5) - noise1
        ) * 50.0; // Reduced gradient multiplier

        force = noiseGrad * intensity * 10.0; // Reduced force

        float hue = frac(noise1 + _IdleTime * _ColorCycleSpeed);
        color = float3(hue, 0.5, 0.7); // Reduced saturation and value
    }
    
    // Apply the generated pattern with oversaturation prevention
    if (intensity > 0.001)
    {
        // Convert HSV to RGB properly
        float3 rgb = hsv2rgb(color);

        // Check existing color intensity to prevent oversaturation
        float existingIntensity = length(baseDye.rgb);
        float maxIntensity = 1.2; // Lower maximum to prevent white oversaturation

        // Reduce new color intensity if existing color is already bright
        float adaptiveIntensity = intensity * saturate((maxIntensity - existingIntensity) / maxIntensity);

        // Apply color with adaptive intensity
        float4 newDye = baseDye + float4(rgb * adaptiveIntensity, 0);

        // More conservative clamping
        newDye.rgb = min(newDye.rgb, float3(1.2, 1.2, 1.2));
        newDye.a = 1.0;

        float2 newVel = baseVel + force * _TimeStep;

        _DyeOut[pix] = newDye;
        _VelocityOut[pix] = float4(newVel, 0, 0);
    }
    else
    {
        _DyeOut[pix] = baseDye;
        _VelocityOut[pix] = float4(baseVel, 0, 0);
    }
}

// Density diffusion kernel for smooth color spreading
[numthreads(8,8,1)]
void DiffuseDye(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float4 center = _Dye[pix];

    // Sample neighboring pixels for diffusion
    float4 left = _Dye[clamp(pix + int2(-1, 0), 0, _SimResolution - 1)];
    float4 right = _Dye[clamp(pix + int2(1, 0), 0, _SimResolution - 1)];
    float4 bottom = _Dye[clamp(pix + int2(0, -1), 0, _SimResolution - 1)];
    float4 top = _Dye[clamp(pix + int2(0, 1), 0, _SimResolution - 1)];

    // Additional diagonal samples for smoother diffusion
    float4 topLeft = _Dye[clamp(pix + int2(-1, 1), 0, _SimResolution - 1)];
    float4 topRight = _Dye[clamp(pix + int2(1, 1), 0, _SimResolution - 1)];
    float4 bottomLeft = _Dye[clamp(pix + int2(-1, -1), 0, _SimResolution - 1)];
    float4 bottomRight = _Dye[clamp(pix + int2(1, -1), 0, _SimResolution - 1)];

    // Calculate diffusion using weighted average
    // Direct neighbors have higher weight than diagonal neighbors
    float4 directNeighbors = (left + right + bottom + top) * 0.25;
    float4 diagonalNeighbors = (topLeft + topRight + bottomLeft + bottomRight) * 0.125;
    float4 neighborAverage = directNeighbors * 0.7 + diagonalNeighbors * 0.3;

    // Apply diffusion based on density difference
    float4 diffusionForce = (neighborAverage - center) * _DensityDiffusion;

    // Apply the diffusion while preserving color intensity
    float4 newDye = center + diffusionForce * _TimeStep;

    // Preserve alpha and ensure positive values
    newDye.a = 1.0;
    newDye.rgb = max(newDye.rgb, float3(0, 0, 0));

    // Optional: Apply slight dissipation during diffusion to prevent accumulation
    newDye.rgb *= 0.999;

    _DyeOut[pix] = newDye;
}
