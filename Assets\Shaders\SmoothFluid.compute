// Advanced GPU fluid simulation with smooth motion and better blending
#pragma kernel AdvectVelocity
#pragma kernel AdvectDye
#pragma kernel AddSplat
#pragma kernel ComputeDivergence
#pragma kernel Jacobi
#pragma kernel SubtractGradient
#pragma kernel AddCurlForce
#pragma kernel DiffuseDye
#pragma target 5.0

// Parameters
int2 _SimResolution;
float _TimeStep;
float _Dissipation;
float _VelocityDissipation;
float _PressureDissipation;
float _Radius;
float4 _SplatColor;
float2 _SplatPos;
float2 _SplatForce;
float _CurlStrength;
float _DyeDiffusion;

// Textures
RWTexture2D<float4> _Velocity;
RWTexture2D<float4> _VelocityOut;
RWTexture2D<float4> _Dye;
RWTexture2D<float4> _DyeOut;
RWTexture2D<float> _Divergence;
RWTexture2D<float> _Pressure;
RWTexture2D<float> _Curl;

// Smooth bilinear sampling function
float4 SampleBilinear(RWTexture2D<float4> tex, float2 uv)
{
    float2 size = float2(_SimResolution);
    float2 pos = uv * size - 0.5;
    int2 i = int2(floor(pos));
    float2 f = frac(pos);
    
    // Clamp to valid range
    i = clamp(i, int2(0,0), _SimResolution - int2(2,2));
    
    float4 a = tex[i];
    float4 b = tex[i + int2(1,0)];
    float4 c = tex[i + int2(0,1)];
    float4 d = tex[i + int2(1,1)];
    
    return lerp(lerp(a, b, f.x), lerp(c, d, f.x), f.y);
}

// Smooth advection with better interpolation
[numthreads(8,8,1)]
void AdvectVelocity(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float2 uv = (float2(pix) + 0.5) / float2(_SimResolution);
    float4 vel = _Velocity[pix];
    
    // Multi-step advection for smoother motion
    float2 k1 = vel.xy * _TimeStep;
    float2 pos1 = uv - k1 * 0.5 / float2(_SimResolution);
    float4 vel1 = SampleBilinear(_Velocity, pos1);
    
    float2 k2 = vel1.xy * _TimeStep;
    float2 pos2 = uv - k2 / float2(_SimResolution);
    pos2 = clamp(pos2, 0.5 / float2(_SimResolution), 1.0 - 0.5 / float2(_SimResolution));
    
    float4 advectedVel = SampleBilinear(_Velocity, pos2);
    advectedVel.xy *= (1.0 - _VelocityDissipation * _TimeStep);
    
    _VelocityOut[pix] = advectedVel;
}

// Smooth dye advection with better blending
[numthreads(8,8,1)]
void AdvectDye(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float2 uv = (float2(pix) + 0.5) / float2(_SimResolution);
    float4 vel = _Velocity[pix];
    
    // Multi-step advection for smoother dye transport
    float2 k1 = vel.xy * _TimeStep;
    float2 pos1 = uv - k1 * 0.5 / float2(_SimResolution);
    float4 vel1 = SampleBilinear(_Velocity, pos1);
    
    float2 k2 = vel1.xy * _TimeStep;
    float2 pos2 = uv - k2 / float2(_SimResolution);
    pos2 = clamp(pos2, 0.5 / float2(_SimResolution), 1.0 - 0.5 / float2(_SimResolution));
    
    float4 advectedDye = SampleBilinear(_Dye, pos2);
    
    // Apply dissipation with smooth falloff
    advectedDye.rgb *= (1.0 - _Dissipation * _TimeStep);
    advectedDye.a = 1.0;
    
    _DyeOut[pix] = advectedDye;
}

// Enhanced splat with smooth falloff and better color mixing
[numthreads(8,8,1)]
void AddSplat(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    float2 uv = (float2(pix) + 0.5) / float2(_SimResolution);
    float2 diff = uv - _SplatPos;
    float dist = length(diff);
    
    // Smooth gaussian falloff with wider range
    float w = exp(-dist * dist / (_Radius * _Radius * 0.3));
    
    if (w > 0.01) // Only process pixels with significant influence
    {
        // Add dye with additive blending for better color mixing
        float4 baseDye = _Dye[pix];
        float4 splatColor = float4(_SplatColor.rgb * w, 1.0);
        
        // Use additive blending to allow colors to mix and create new colors
        float4 mixedDye = baseDye + splatColor * 0.8; // Slightly reduce intensity to prevent oversaturation
        
        // Clamp to prevent over-bright colors but allow HDR range
        mixedDye.rgb = min(mixedDye.rgb, float3(3.0, 3.0, 3.0));
        mixedDye.a = 1.0;
        
        _DyeOut[pix] = mixedDye;
        
        // Add velocity with directional force and some turbulence
        float2 baseVel = _Velocity[pix].xy;
        float2 forceDir = _SplatForce;
        
        // Add some radial component for more interesting motion
        float2 radialForce = normalize(diff) * length(_SplatForce) * 0.2;
        float2 totalForce = forceDir + radialForce;
        
        float2 addedVel = totalForce * w;
        _VelocityOut[pix] = float4(baseVel + addedVel, 0, 0);
    }
    else
    {
        _DyeOut[pix] = _Dye[pix];
        _VelocityOut[pix] = _Velocity[pix];
    }
}

// Compute curl for vorticity confinement
[numthreads(8,8,1)]
void AddCurlForce(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;
    if (pix.x < 1 || pix.x >= _SimResolution.x - 1 || pix.y < 1 || pix.y >= _SimResolution.y - 1) return;

    // Compute curl (vorticity)
    float2 vL = _Velocity[pix + int2(-1, 0)].xy;
    float2 vR = _Velocity[pix + int2(1, 0)].xy;
    float2 vB = _Velocity[pix + int2(0, -1)].xy;
    float2 vT = _Velocity[pix + int2(0, 1)].xy;
    
    float curl = (vR.y - vL.y) - (vT.x - vB.x);
    _Curl[pix] = curl;
    
    // Compute curl gradient
    float cL = _Curl[pix + int2(-1, 0)];
    float cR = _Curl[pix + int2(1, 0)];
    float cB = _Curl[pix + int2(0, -1)];
    float cT = _Curl[pix + int2(0, 1)];
    
    float2 curlGrad = float2((cR - cL) * 0.5, (cT - cB) * 0.5);
    float curlGradMag = length(curlGrad);
    
    if (curlGradMag > 0.0001)
    {
        curlGrad /= curlGradMag;
        float2 curlForce = float2(curlGrad.y, -curlGrad.x) * curl * _CurlStrength;
        
        float4 vel = _Velocity[pix];
        vel.xy += curlForce * _TimeStep;
        _VelocityOut[pix] = vel;
    }
    else
    {
        _VelocityOut[pix] = _Velocity[pix];
    }
}

// Dye diffusion for better color mixing
[numthreads(8,8,1)]
void DiffuseDye(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;
    if (pix.x < 1 || pix.x >= _SimResolution.x - 1 || pix.y < 1 || pix.y >= _SimResolution.y - 1) return;

    float4 center = _Dye[pix];
    float4 left = _Dye[pix + int2(-1, 0)];
    float4 right = _Dye[pix + int2(1, 0)];
    float4 bottom = _Dye[pix + int2(0, -1)];
    float4 top = _Dye[pix + int2(0, 1)];
    
    // Laplacian diffusion
    float4 laplacian = (left + right + bottom + top - center * 4.0);
    float4 diffused = center + laplacian * _DyeDiffusion * _TimeStep;
    
    _DyeOut[pix] = diffused;
}

// Standard divergence computation
[numthreads(8,8,1)]
void ComputeDivergence(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    int2 left = max(pix + int2(-1,0), int2(0,0));
    int2 right = min(pix + int2(1,0), _SimResolution - int2(1,1));
    int2 down = max(pix + int2(0,-1), int2(0,0));
    int2 up = min(pix + int2(0,1), _SimResolution - int2(1,1));

    float2 vl = _Velocity[left].xy;
    float2 vr = _Velocity[right].xy;
    float2 vd = _Velocity[down].xy;
    float2 vu = _Velocity[up].xy;

    float hx = 1.0 / _SimResolution.x;
    float hy = 1.0 / _SimResolution.y;

    float dx = (vr.x - vl.x) * 0.5 / hx;
    float dy = (vu.y - vd.y) * 0.5 / hy;

    _Divergence[pix] = dx + dy;
}

// Jacobi pressure solver
[numthreads(8,8,1)]
void Jacobi(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    int2 left = max(pix + int2(-1,0), int2(0,0));
    int2 right = min(pix + int2(1,0), _SimResolution - int2(1,1));
    int2 down = max(pix + int2(0,-1), int2(0,0));
    int2 up = min(pix + int2(0,1), _SimResolution - int2(1,1));

    float pL = _Pressure[left];
    float pR = _Pressure[right];
    float pD = _Pressure[down];
    float pU = _Pressure[up];
    float b = _Divergence[pix];

    float p = (pL + pR + pD + pU + (-1.0) * b) * 0.25;
    p *= (1.0 - _PressureDissipation * _TimeStep);
    _Pressure[pix] = p;
}

// Subtract pressure gradient
[numthreads(8,8,1)]
void SubtractGradient(uint3 id : SV_DispatchThreadID)
{
    int2 pix = int2(id.xy);
    if (pix.x >= _SimResolution.x || pix.y >= _SimResolution.y) return;

    int2 left = max(pix + int2(-1,0), int2(0,0));
    int2 right = min(pix + int2(1,0), _SimResolution - int2(1,1));
    int2 down = max(pix + int2(0,-1), int2(0,0));
    int2 up = min(pix + int2(0,1), _SimResolution - int2(1,1));

    float pL = _Pressure[left];
    float pR = _Pressure[right];
    float pD = _Pressure[down];
    float pU = _Pressure[up];

    float hx = 1.0 / _SimResolution.x;
    float hy = 1.0 / _SimResolution.y;

    float2 vel = _Velocity[pix].xy;
    vel.x -= 0.5 * (pR - pL) / hx;
    vel.y -= 0.5 * (pU - pD) / hy;

    vel *= (1.0 - _VelocityDissipation * _TimeStep);
    _VelocityOut[pix] = float4(vel, 0, 0);
}