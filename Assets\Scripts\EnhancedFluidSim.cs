using UnityEngine;
using UnityEngine.UI;

/// <summary>
/// Enhanced fluid simulation with advanced idle animation patterns
/// Uses the EnhancedFluid.compute shader for sophisticated GPU-based animation
/// </summary>
public class EnhancedFluidSim : MonoBehaviour
{
    [Header("Core Settings")]
    public ComputeShader enhancedFluidCompute;
    public RawImage targetImage;
    public Material blitMaterial;
    
    [Header("Simulation Parameters")]
    [Range(64, 1024)]
    public int simResolution = 256;
    [Range(0.001f, 0.1f)]
    public float timeStep = 0.016f;
    [Range(0.001f, 0.02f)]
    public float dyeDissipation = 0.005f;  // Increased to prevent accumulation
    [Range(0.001f, 0.02f)]
    public float velocityDissipation = 0.008f;  // Increased for stability
    [Range(0.0f, 0.1f)]
    public float pressureDissipation = 0.01f;
    [Range(10, 50)]
    public int jacobiIterations = 20;

    [Head<PERSON>("Density Diffusion")]
    public bool enableDensityDiffusion = true;
    [Range(0.0f, 0.01f)]
    public float densityDiffusion = 0.001f;  // Controls how dye spreads
    [Range(1, 5)]
    public int diffusionIterations = 2;  // Number of diffusion steps per frame
    [Range(0.0f, 2.0f)]
    public float diffusionStrength = 1.0f;  // Overall diffusion intensity

    [Header("Idle Animation")]
    public bool enableIdleAnimation = true;
    [Range(0.1f, 2.0f)]
    public float idleAnimationSpeed = 1.0f;
    [Range(0.1f, 1.0f)]
    public float idleIntensity = 0.3f;  // Reduced to prevent oversaturation
    [Range(0.01f, 0.2f)]
    public float colorCycleSpeed = 0.06f;
    
    [Header("Animation Patterns")]
    public IdlePatternType currentPattern = IdlePatternType.Spiral;
    [Range(0.1f, 2.0f)]
    public float patternScale = 1.0f;
    [Range(0.1f, 3.0f)]
    public float spiralSpeed = 0.5f;
    [Range(0.05f, 0.5f)]
    public float waveAmplitude = 0.2f;
    [Range(0.1f, 0.4f)]
    public float orbitRadius = 0.25f;
    
    [Header("Visual")]
    [Range(0.02f, 0.2f)]
    public float splatRadius = 0.08f;
    
    public enum IdlePatternType
    {
        Spiral = 0,
        Wave = 1,
        Orbit = 2,
        PerlinNoise = 3,
        Cycling = 4  // Cycles through all patterns
    }
    
    // Render textures
    private RenderTexture velocityRT, velocityRT2;
    private RenderTexture dyeRT, dyeRT2;
    private RenderTexture pressureRT, divergenceRT, curlRT;
    
    // Kernel IDs
    private int kernelAdvectVel = -1;
    private int kernelAdvectDye = -1;
    private int kernelAddSplat = -1;
    private int kernelDivergence = -1;
    private int kernelJacobi = -1;
    private int kernelSubGrad = -1;
    private int kernelCurlForce = -1;
    private int kernelIdlePattern = -1;
    private int kernelDiffuseDye = -1;
    
    // Animation state
    private float idleTimer = 0f;
    private float patternTimer = 0f;
    private int currentPatternIndex = 0;
    private float patternSwitchInterval = 5f; // Switch patterns every 5 seconds
    
    void Start()
    {
        if (enhancedFluidCompute == null)
        {
            Debug.LogError("EnhancedFluidSim: No compute shader assigned!");
            enabled = false;
            return;
        }
        
        if (!SystemInfo.supportsComputeShaders)
        {
            Debug.LogError("EnhancedFluidSim: Compute shaders not supported!");
            enabled = false;
            return;
        }
        
        InitializeSimulation();
    }
    
    void InitializeSimulation()
    {
        CreateRenderTextures();
        CacheKernelIDs();
        
        // Assign texture to UI
        if (targetImage != null && dyeRT != null)
        {
            targetImage.texture = dyeRT;
            targetImage.material = blitMaterial;
        }
        
        // Add initial splats for immediate visual feedback
        AddInitialSplats();
        
        Debug.Log("EnhancedFluidSim: Advanced fluid simulation with idle animation initialized");
    }
    
    void CreateRenderTextures()
    {
        ReleaseRenderTextures();
        
        var rtDesc = new RenderTextureDescriptor(simResolution, simResolution)
        {
            colorFormat = RenderTextureFormat.ARGBFloat,
            enableRandomWrite = true,
            sRGB = false
        };
        
        // Velocity textures
        velocityRT = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear };
        velocityRT.Create();
        velocityRT2 = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear };
        velocityRT2.Create();
        
        // Dye textures
        dyeRT = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear };
        dyeRT.Create();
        dyeRT2 = new RenderTexture(rtDesc) { filterMode = FilterMode.Bilinear };
        dyeRT2.Create();
        
        // Pressure and divergence (single channel)
        pressureRT = new RenderTexture(simResolution, simResolution, 0, RenderTextureFormat.RFloat)
        { enableRandomWrite = true, filterMode = FilterMode.Bilinear };
        pressureRT.Create();
        
        divergenceRT = new RenderTexture(simResolution, simResolution, 0, RenderTextureFormat.RFloat)
        { enableRandomWrite = true, filterMode = FilterMode.Bilinear };
        divergenceRT.Create();
        
        curlRT = new RenderTexture(simResolution, simResolution, 0, RenderTextureFormat.RFloat)
        { enableRandomWrite = true, filterMode = FilterMode.Bilinear };
        curlRT.Create();
        
        // Clear all textures
        ClearTextures();
    }
    
    void ClearTextures()
    {
        Graphics.Blit(Texture2D.blackTexture, velocityRT);
        Graphics.Blit(Texture2D.blackTexture, velocityRT2);
        Graphics.Blit(Texture2D.blackTexture, dyeRT);
        Graphics.Blit(Texture2D.blackTexture, dyeRT2);
        Graphics.Blit(Texture2D.blackTexture, pressureRT);
        Graphics.Blit(Texture2D.blackTexture, divergenceRT);
        Graphics.Blit(Texture2D.blackTexture, curlRT);
    }
    
    void CacheKernelIDs()
    {
        kernelAdvectVel = TryFindKernel("AdvectVelocity");
        kernelAdvectDye = TryFindKernel("AdvectDye");
        kernelAddSplat = TryFindKernel("AddSplat");
        kernelDivergence = TryFindKernel("ComputeDivergence");
        kernelJacobi = TryFindKernel("Jacobi");
        kernelSubGrad = TryFindKernel("SubtractGradient");
        kernelCurlForce = TryFindKernel("AddCurlForce");
        kernelIdlePattern = TryFindKernel("GenerateIdlePattern");
        kernelDiffuseDye = TryFindKernel("DiffuseDye");
    }
    
    int TryFindKernel(string name)
    {
        try
        {
            return enhancedFluidCompute.FindKernel(name);
        }
        catch
        {
            Debug.LogWarning($"EnhancedFluidSim: Kernel '{name}' not found in compute shader");
            return -1;
        }
    }
    
    void AddInitialSplats()
    {
        // Add some initial colorful splats
        AddSplat(new Vector2(0.3f, 0.3f), Color.red, new Vector2(20f, 15f));
        AddSplat(new Vector2(0.7f, 0.3f), Color.green, new Vector2(-20f, 15f));
        AddSplat(new Vector2(0.3f, 0.7f), Color.blue, new Vector2(20f, -15f));
        AddSplat(new Vector2(0.7f, 0.7f), Color.yellow, new Vector2(-20f, -15f));
        AddSplat(new Vector2(0.5f, 0.5f), Color.magenta, new Vector2(0f, 25f));
    }
    
    void Update()
    {
        if (dyeRT == null) return;
        
        // Handle pattern cycling
        if (currentPattern == IdlePatternType.Cycling)
        {
            patternTimer += Time.deltaTime;
            if (patternTimer >= patternSwitchInterval)
            {
                patternTimer = 0f;
                currentPatternIndex = (currentPatternIndex + 1) % 4; // Cycle through 4 patterns
            }
        }
        
        // Handle user input
        HandleInput();
        
        // Run simulation
        StepSimulation();
        
        // Update UI texture
        if (targetImage != null && targetImage.texture != dyeRT)
            targetImage.texture = dyeRT;
    }
    
    void HandleInput()
    {
        if (Input.GetMouseButtonDown(0))
        {
            Vector2 mousePos = Input.mousePosition;
            if (targetImage != null)
            {
                RectTransform rectTransform = targetImage.rectTransform;
                Vector2 localPoint;
                if (RectTransformUtility.ScreenPointToLocalPointInRectangle(
                    rectTransform, mousePos, null, out localPoint))
                {
                    Vector2 uv = new Vector2(
                        (localPoint.x + rectTransform.rect.width * 0.5f) / rectTransform.rect.width,
                        (localPoint.y + rectTransform.rect.height * 0.5f) / rectTransform.rect.height
                    );
                    
                    if (uv.x >= 0 && uv.x <= 1 && uv.y >= 0 && uv.y <= 1)
                    {
                        Color randomColor = Color.HSVToRGB(Random.value, 0.8f, 1.0f);
                        Vector2 randomForce = new Vector2(
                            (Random.value - 0.5f) * 40f,
                            (Random.value - 0.5f) * 40f
                        );
                        AddSplat(uv, randomColor, randomForce);
                    }
                }
            }
        }
    }
    
    void StepSimulation()
    {
        SetCommonParameters();
        
        // Step 1: Advect velocity
        if (kernelAdvectVel >= 0)
        {
            enhancedFluidCompute.SetFloat("_VelocityDissipation", velocityDissipation);
            enhancedFluidCompute.SetTexture(kernelAdvectVel, "_Velocity", velocityRT);
            enhancedFluidCompute.SetTexture(kernelAdvectVel, "_VelocityOut", velocityRT2);
            DispatchKernel(kernelAdvectVel);
            SwapTextures(ref velocityRT, ref velocityRT2);
        }
        
        // Step 2: Add curl forces for turbulence
        if (kernelCurlForce >= 0)
        {
            enhancedFluidCompute.SetTexture(kernelCurlForce, "_Velocity", velocityRT);
            enhancedFluidCompute.SetTexture(kernelCurlForce, "_VelocityOut", velocityRT2);
            enhancedFluidCompute.SetTexture(kernelCurlForce, "_Curl", curlRT);
            DispatchKernel(kernelCurlForce);
            SwapTextures(ref velocityRT, ref velocityRT2);
        }
        
        // Step 3: Compute divergence
        if (kernelDivergence >= 0)
        {
            enhancedFluidCompute.SetTexture(kernelDivergence, "_Velocity", velocityRT);
            enhancedFluidCompute.SetTexture(kernelDivergence, "_Divergence", divergenceRT);
            DispatchKernel(kernelDivergence);
        }
        
        // Step 4: Solve pressure (Jacobi iterations)
        if (kernelJacobi >= 0)
        {
            Graphics.Blit(Texture2D.blackTexture, pressureRT);
            enhancedFluidCompute.SetFloat("_PressureDissipation", pressureDissipation);
            enhancedFluidCompute.SetTexture(kernelJacobi, "_Divergence", divergenceRT);
            enhancedFluidCompute.SetTexture(kernelJacobi, "_Pressure", pressureRT);
            
            for (int i = 0; i < jacobiIterations; i++)
            {
                DispatchKernel(kernelJacobi);
            }
        }
        
        // Step 5: Subtract pressure gradient
        if (kernelSubGrad >= 0)
        {
            enhancedFluidCompute.SetTexture(kernelSubGrad, "_Pressure", pressureRT);
            enhancedFluidCompute.SetTexture(kernelSubGrad, "_Velocity", velocityRT);
            enhancedFluidCompute.SetTexture(kernelSubGrad, "_VelocityOut", velocityRT2);
            DispatchKernel(kernelSubGrad);
            SwapTextures(ref velocityRT, ref velocityRT2);
        }
        
        // Step 6: Generate idle animation patterns
        if (enableIdleAnimation && kernelIdlePattern >= 0)
        {
            idleTimer += Time.deltaTime * idleAnimationSpeed;
            
            int patternType = currentPattern == IdlePatternType.Cycling ? 
                currentPatternIndex : (int)currentPattern;
            
            enhancedFluidCompute.SetFloat("_IdleTime", idleTimer);
            enhancedFluidCompute.SetFloat("_PatternScale", patternScale);
            enhancedFluidCompute.SetFloat("_ColorCycleSpeed", colorCycleSpeed);
            enhancedFluidCompute.SetInt("_PatternType", patternType);
            enhancedFluidCompute.SetFloat("_SpiralSpeed", spiralSpeed);
            enhancedFluidCompute.SetFloat("_WaveAmplitude", waveAmplitude);
            enhancedFluidCompute.SetFloat("_OrbitRadius", orbitRadius);
            enhancedFluidCompute.SetFloat("_IdleIntensity", idleIntensity);
            
            enhancedFluidCompute.SetTexture(kernelIdlePattern, "_Dye", dyeRT);
            enhancedFluidCompute.SetTexture(kernelIdlePattern, "_DyeOut", dyeRT2);
            enhancedFluidCompute.SetTexture(kernelIdlePattern, "_Velocity", velocityRT);
            enhancedFluidCompute.SetTexture(kernelIdlePattern, "_VelocityOut", velocityRT2);
            
            DispatchKernel(kernelIdlePattern);
            SwapTextures(ref dyeRT, ref dyeRT2);
            SwapTextures(ref velocityRT, ref velocityRT2);
        }
        
        // Step 7: Apply density diffusion (if enabled)
        if (enableDensityDiffusion && kernelDiffuseDye >= 0)
        {
            enhancedFluidCompute.SetFloat("_DensityDiffusion", densityDiffusion * diffusionStrength);
            enhancedFluidCompute.SetTexture(kernelDiffuseDye, "_Dye", dyeRT);
            enhancedFluidCompute.SetTexture(kernelDiffuseDye, "_DyeOut", dyeRT2);

            for (int i = 0; i < diffusionIterations; i++)
            {
                DispatchKernel(kernelDiffuseDye);
                SwapTextures(ref dyeRT, ref dyeRT2);
            }
        }

        // Step 8: Advect dye
        if (kernelAdvectDye >= 0)
        {
            enhancedFluidCompute.SetFloat("_Dissipation", dyeDissipation);
            enhancedFluidCompute.SetTexture(kernelAdvectDye, "_Velocity", velocityRT);
            enhancedFluidCompute.SetTexture(kernelAdvectDye, "_Dye", dyeRT);
            enhancedFluidCompute.SetTexture(kernelAdvectDye, "_DyeOut", dyeRT2);
            DispatchKernel(kernelAdvectDye);
            SwapTextures(ref dyeRT, ref dyeRT2);
        }
    }
    
    void SetCommonParameters()
    {
        enhancedFluidCompute.SetInts("_SimResolution", new int[] { simResolution, simResolution });
        enhancedFluidCompute.SetFloat("_TimeStep", timeStep);
    }
    
    void DispatchKernel(int kernel)
    {
        int groupX = Mathf.CeilToInt(simResolution / 8.0f);
        int groupY = Mathf.CeilToInt(simResolution / 8.0f);
        enhancedFluidCompute.Dispatch(kernel, groupX, groupY, 1);
    }
    
    void SwapTextures(ref RenderTexture a, ref RenderTexture b)
    {
        RenderTexture temp = a;
        a = b;
        b = temp;
    }
    
    public void AddSplat(Vector2 position, Color color, Vector2 force)
    {
        if (kernelAddSplat < 0) return;
        
        enhancedFluidCompute.SetFloat("_Radius", splatRadius);
        enhancedFluidCompute.SetFloats("_SplatColor", new float[] { color.r, color.g, color.b, 1.0f });
        enhancedFluidCompute.SetFloats("_SplatPos", new float[] { position.x, position.y });
        enhancedFluidCompute.SetFloats("_SplatForce", new float[] { force.x, force.y });
        
        enhancedFluidCompute.SetTexture(kernelAddSplat, "_Dye", dyeRT);
        enhancedFluidCompute.SetTexture(kernelAddSplat, "_DyeOut", dyeRT2);
        enhancedFluidCompute.SetTexture(kernelAddSplat, "_Velocity", velocityRT);
        enhancedFluidCompute.SetTexture(kernelAddSplat, "_VelocityOut", velocityRT2);
        
        DispatchKernel(kernelAddSplat);
        SwapTextures(ref dyeRT, ref dyeRT2);
        SwapTextures(ref velocityRT, ref velocityRT2);
    }
    
    void OnDestroy()
    {
        ReleaseRenderTextures();
    }
    
    void ReleaseRenderTextures()
    {
        if (velocityRT != null) { velocityRT.Release(); velocityRT = null; }
        if (velocityRT2 != null) { velocityRT2.Release(); velocityRT2 = null; }
        if (dyeRT != null) { dyeRT.Release(); dyeRT = null; }
        if (dyeRT2 != null) { dyeRT2.Release(); dyeRT2 = null; }
        if (pressureRT != null) { pressureRT.Release(); pressureRT = null; }
        if (divergenceRT != null) { divergenceRT.Release(); divergenceRT = null; }
        if (curlRT != null) { curlRT.Release(); curlRT = null; }
    }
}
